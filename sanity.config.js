import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemaTypes'
import {csvImport} from './plugins/sanity-plugin-csv-import/src/index.jsx'
import {dashboardPlugin} from './plugins/dashboard/index'
import {customDeskStructure} from './deskStructure'

// Data Export Plugin
import {definePlugin} from 'sanity'
import {DownloadIcon, CalendarIcon} from '@sanity/icons'
import {DataExportTool} from './plugins/data-export/DataExportTool'
import {SchedulerTool} from './plugins/content-scheduler/SchedulerTool'

const dataExportPlugin = definePlugin({
  name: 'data-export',
  tools: [
    {
      name: 'data-export',
      title: 'Data Export',
      icon: DownloadIcon,
      component: DataExportTool
    },
  ],
})

const contentSchedulerPlugin = definePlugin({
  name: 'content-scheduler',
  tools: [
    {
      name: 'content-scheduler',
      title: 'Content Scheduler',
      icon: CalendarIcon,
      component: SchedulerTool
    },
  ],
})

export default defineConfig({
  name: 'default',
  title: 'Northern Nepalese United FC Studio',

  projectId: '9at30otk',
  dataset: 'production',

  plugins: [
    structureTool({
      structure: customDeskStructure
    }),
    visionTool(),
    dashboardPlugin,
    csvImport(),
    dataExportPlugin,
    contentSchedulerPlugin
  ],

  schema: {
    types: schemaTypes,
  },
})