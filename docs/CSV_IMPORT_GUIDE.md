# CSV Import Guide for Brisbane FC Website

This guide explains how to use the CSV Import tool to easily add player and staff data to the Brisbane FC website.

## Accessing the CSV Import Tool

1. Log in to the Sanity Studio at http://localhost:3333 (or your production URL)
2. Look for the "CSV Import" tool in the sidebar menu
3. Click on it to open the import interface

## Preparing Your CSV Files

### For Players

Create a CSV file with the following columns:

- **Required columns:**
  - `name` - Player's full name
  - `position` - Must be one of: Goalkeeper, Defender, Midfielder, Forward

- **Optional columns:**
  - `specificPosition` - Detailed position (e.g., "Right Winger", "Centre-Back", "Central Midfielder"). If not provided, will use the main position
  - `role` - Team role: Player, Captain, or Vice Captain (defaults to "Player")
  - `jerseyNumber` - Player's jersey number
  - `bio` - Brief player biography
  - `isActive` - Set to "false" for inactive players (defaults to true)
  - `appearances` - Number of appearances
  - `goals` - Number of goals (for non-goalkeepers)
  - `cleanSheets` - Number of clean sheets (for goalkeepers)
  - `assists` - Number of assists (for non-goalkeepers)

Example player CSV:
```
name,position,specificPosition,role,jerseyNumber,bio,isActive,appearances,goals,assists,clean<PERSON>heets
<PERSON>e,Forward,<PERSON>r,Captain,10,"<PERSON> is a talented forward with over 5 years of experience.",true,25,12,5,
<PERSON> <PERSON>,<PERSON>fielder,<PERSON> <PERSON>fielder,<PERSON> Captain,8,"<PERSON> is known for her creative passes and midfield control.",true,30,5,15,
<PERSON> <PERSON>,De<PERSON>,<PERSON>-Back,Player,4,"<PERSON> is a solid defender who excels in aerial duels.",true,28,2,3,
<PERSON>huban <PERSON>ng,Forward,Right Winger,Player,25,"Bhuban is a versatile forward, capable of scoring goals and creating chances from the wing.",true,6,4,4,
```

### For Staff

Create a CSV file with the following columns:

- **Required columns:**
  - `name` - Staff member's full name

- **Optional columns:**
  - `role` - Staff role (Head Coach, Assistant Coach, Team Manager, etc.)
  - `bio` - Brief staff biography
  - `isActive` - Set to "false" for inactive staff (defaults to true)
  - `email` - Contact email address
  - `phone` - Contact phone number

Example staff CSV:
```
name,role,bio,isActive,email,phone
Alex Johnson,Head Coach,"Alex has over 15 years of coaching experience.",true,<EMAIL>,************
Sarah Williams,Assistant Coach,"Sarah specializes in player development.",true,<EMAIL>,
```

## Importing Data

1. In the CSV Import tool, click either "Select Players CSV" or "Select Staff CSV" depending on what you want to import
2. Select your prepared CSV file from your computer
3. Wait for the import process to complete
4. Review the import results, including any errors or warnings

## After Import

- You can view the imported data in the respective "Players" or "Staff" sections in Sanity Studio
- Add images to players/staff by editing them individually
- Make any additional adjustments to the imported data as needed

## Troubleshooting

If you encounter errors during import:

1. Check that your CSV file format is correct (comma-separated values)
2. Verify that required fields (name, position for players) are present
3. For player positions, make sure they exactly match one of: Goalkeeper, Defender, Midfielder, Forward
4. Check that numeric fields (jerseyNumber, appearances, etc.) contain only numbers
5. Try importing a smaller batch of records to identify specific issues