# Analytics Implementation Guide

## Overview

This document outlines the comprehensive analytics implementation for the Northern Nepalese United FC (NNUFC) website, including Microsoft Clarity and preparation for Google Analytics.

## Analytics Services

### 1. Microsoft Clarity ✅ **ACTIVE**
- **Service**: Microsoft Clarity
- **ID**: `rp28jewrfj`
- **Purpose**: User behavior tracking, heatmaps, session recordings
- **Environment Variable**: `NEXT_PUBLIC_CLARITY_ID`

### 2. Google Analytics 🔄 **READY FOR SETUP**
- **Service**: Google Analytics 4 (GA4)
- **Purpose**: Traffic analysis, conversion tracking, audience insights
- **Environment Variable**: `NEXT_PUBLIC_GA_MEASUREMENT_ID` (to be added)

## Implementation Details

### Environment Variables
```bash
# Analytics Configuration
NEXT_PUBLIC_CLARITY_ID="rp28jewrfj"
NEXT_PUBLIC_GA_MEASUREMENT_ID="" # Add when Google Analytics is set up
```

### Core Components

#### 1. Analytics Component (`/components/Analytics.tsx`)
- **Microsoft Clarity**: Loads tracking script with proper configuration
- **Google Analytics**: Ready for GA4 implementation
- **Environment-aware**: Only loads in production or when explicitly enabled
- **Type-safe**: Includes TypeScript declarations for global analytics objects

#### 2. Custom Analytics Hook (`/hooks/useNNUFCAnalytics.ts`)
- **NNUFC-specific events**: Tailored tracking for football club activities
- **Comprehensive tracking**: Navigation, team interactions, content engagement
- **Performance monitoring**: Page load times, scroll depth, time on page
- **Error tracking**: Automatic error logging and reporting

### Event Tracking Categories

#### Navigation & Page Views
- `navigation` - Menu and link clicks
- `nnufc_page_view` - Enhanced page view with context
- `page_performance` - Load time tracking

#### Team & Player Interactions
- `player_view` - Player profile views
- `staff_view` - Staff profile views
- `team_section_engagement` - Team page interactions

#### Content Engagement
- `news_article_view` - News article reads
- `news_share` - Social sharing of articles
- `event_view` - Event/fixture page views
- `event_interest` - Event engagement actions

#### Contact & Conversion
- `contact_form_submit` - Form submissions
- `contact_info_click` - Contact detail interactions
- `junior_academy_interest` - Academy program interest
- `junior_academy_enquiry` - Academy enrollment inquiries

#### Sponsor & Partnership
- `sponsor_click` - Sponsor link clicks
- `sponsor_engagement` - Sponsor page interactions

#### User Behavior
- `search` - Site search usage
- `filter_applied` - Content filtering
- `scroll_depth` - Page scroll engagement
- `time_on_page` - Session duration tracking

## Microsoft Clarity Features

### What Clarity Tracks:
1. **Heatmaps**: Click and scroll patterns
2. **Session Recordings**: User interaction videos
3. **User Flows**: Navigation patterns
4. **Performance Metrics**: Page load and interaction times
5. **Error Detection**: JavaScript errors and issues
6. **Mobile Experience**: Touch interactions and mobile-specific behavior

### Clarity Dashboard Access:
- **URL**: https://clarity.microsoft.com/
- **Project ID**: `rp28jewrfj`
- **Data Retention**: 3 months (free tier)

### Key Metrics to Monitor:
- **Dead Clicks**: Clicks that don't result in actions
- **Rage Clicks**: Rapid repeated clicks indicating frustration
- **Scroll Reach**: How far users scroll on pages
- **Session Duration**: Time spent on site
- **Error Rate**: JavaScript errors affecting user experience

## Google Analytics Setup (Future)

### When Ready to Add GA4:
1. **Create GA4 Property**: Set up in Google Analytics
2. **Get Measurement ID**: Format `G-XXXXXXXXXX`
3. **Add to Environment**: Update `NEXT_PUBLIC_GA_MEASUREMENT_ID`
4. **Deploy**: Analytics component will automatically include GA4

### GA4 Features Ready:
- **Enhanced E-commerce**: Track sponsor interactions
- **Custom Events**: NNUFC-specific tracking
- **Audience Building**: Segment users by interest
- **Conversion Tracking**: Form submissions and engagement
- **Cross-platform Tracking**: Web and future mobile app

## Privacy & Compliance

### Data Protection:
- **No PII Collection**: Analytics avoid personal information
- **Cookie Consent**: Ready for cookie banner implementation
- **GDPR Compliance**: Anonymized data collection
- **User Control**: Opt-out mechanisms available

### Security:
- **Environment Variables**: Sensitive IDs stored securely
- **Script Loading**: Secure HTTPS loading with integrity checks
- **Error Handling**: Graceful fallbacks if analytics fail

## Usage Examples

### Basic Page Tracking
```typescript
import { useNNUFCAnalytics } from '@/hooks/useNNUFCAnalytics'

function MyComponent() {
  const analytics = useNNUFCAnalytics()
  
  useEffect(() => {
    analytics.trackNNUFCPageView('my-page')
  }, [])
}
```

### Event Tracking
```typescript
const handlePlayerClick = (player) => {
  analytics.trackPlayerView(player.name, player.position)
}

const handleNewsShare = (article, platform) => {
  analytics.trackNewsShare(article.title, platform)
}
```

### Performance Monitoring
```typescript
useEffect(() => {
  const startTime = performance.now()
  
  const handleLoad = () => {
    const loadTime = performance.now() - startTime
    analytics.trackPerformance('team', loadTime)
  }
  
  window.addEventListener('load', handleLoad)
}, [])
```

## Development vs Production

### Development Mode:
- **Console Logging**: Events logged to browser console
- **Analytics Disabled**: No actual data sent to services
- **Debug Mode**: Detailed event information displayed

### Production Mode:
- **Full Tracking**: All analytics services active
- **Optimized Loading**: Scripts loaded with proper timing
- **Error Reporting**: Issues tracked and reported

## Monitoring & Insights

### Key Performance Indicators (KPIs):
1. **User Engagement**:
   - Average session duration
   - Pages per session
   - Bounce rate

2. **Content Performance**:
   - Most viewed team members
   - Popular news articles
   - Event engagement rates

3. **User Journey**:
   - Common navigation paths
   - Drop-off points
   - Conversion funnels

4. **Technical Performance**:
   - Page load times
   - Error rates
   - Mobile vs desktop usage

### Regular Reporting:
- **Weekly**: User behavior patterns
- **Monthly**: Content performance analysis
- **Quarterly**: Technical performance review
- **Annually**: Comprehensive analytics audit

## Troubleshooting

### Common Issues:
1. **Analytics Not Loading**:
   - Check environment variables
   - Verify production deployment
   - Check browser console for errors

2. **Events Not Tracking**:
   - Confirm event names and parameters
   - Check network requests in dev tools
   - Verify analytics service status

3. **Performance Impact**:
   - Scripts load asynchronously
   - Minimal impact on page speed
   - Monitor Core Web Vitals

### Debug Commands:
```javascript
// Check if Clarity is loaded
window.clarity && console.log('Clarity loaded')

// Check if GA is loaded
window.gtag && console.log('Google Analytics loaded')

// View current analytics state
console.log('Analytics Environment:', {
  clarityId: process.env.NEXT_PUBLIC_CLARITY_ID,
  gaId: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID
})
```

## Future Enhancements

### Planned Features:
1. **Advanced Segmentation**: User type classification
2. **A/B Testing**: Content and design experiments
3. **Real-time Dashboards**: Live analytics monitoring
4. **Automated Reporting**: Weekly/monthly email reports
5. **Cross-platform Tracking**: Mobile app integration

### Integration Opportunities:
- **CRM Integration**: Connect with member management
- **Email Marketing**: Track campaign effectiveness
- **Social Media**: Monitor social traffic and engagement
- **E-commerce**: Track merchandise and ticket sales
