# Northern Nepalese United FC Website: Schema Documentation

This document provides detailed information about the Sanity schema types used in the Northern Nepalese United FC website project. It serves as a reference for developers and content editors.

## Overview

The Northern Nepalese United FC website uses Sanity.io as its headless CMS. The content is structured using the following schema types:

1. **Event/Fixture** - For matches, training sessions, and social events
2. **News Article** - For club news and updates
3. **Sponsor** - For club sponsors and partners
4. **Gallery Image** - For photos and media
5. **Player** - For team roster management
6. **Staff** - For coaching staff and team management

## Image Upload Requirements and Constraints

All images across the website must meet the following requirements for consistency and quality:

### Image Requirements
- **Required Alt Text**: All images must have alternative text for accessibility
- **Recommended Dimensions**:
  - Player/Staff Photos: 600x800px minimum (3:4 aspect ratio)
  - News Images: 1200x630px minimum (16:9 aspect ratio preferred)
  - Sponsor Logos: 400x400px minimum (square or landscape orientation)
  - Gallery Images: 1200x800px minimum (3:2 aspect ratio preferred)
- **Format**: JPG, PNG or WebP formats accepted
- **File Size**: Maximum 2MB per image
- **Quality**: High quality, clear images without excessive compression artifacts
- **Hotspot Feature**: Use the hotspot feature in Sanity to adjust focal point for responsive cropping

### Additional Notes
- The CMS will maintain image metadata including EXIF data when available
- For player and staff photos, consistent framing and background is recommended
- Team photos should be clear enough to identify individual players

## Schema Types in Detail

### Event/Fixture (`event`)

Events and fixtures represent scheduled activities such as matches, training sessions, and social events.

#### Fields:

| Field Name | Type | Description | Validation |
|------------|------|-------------|------------|
| `title` | String | The title of the event | Required |
| `slug` | Slug | URL-friendly identifier, auto-generated from title | Required |
| `date` | DateTime | Date and time of the event | Required |
| `location` | String | Where the event takes place | Optional |
| `eventImage` | Image | Image representing the event | Optional, alt text required |
| `eventType` | String | Type of event (match, training, social, other) | Default: 'match' |
| `opponent` | String | Opponent team name (for matches only) | Hidden if not a match |
| `homeOrAway` | String | Whether it's a home or away match | Hidden if not a match |
| `result` | String | Match result (e.g., "Won 2-1") | Hidden if not a match |
| `description` | Block content | Detailed description of the event | Optional |

#### Preview Configuration:

The event preview in the Sanity Studio shows:
- Title prefixed with the event type (e.g., "MATCH: Season Opener")
- Subtitle showing the date and time
- Thumbnail of the event image (when available)

### News Article (`newsArticle`)

News articles are used for club announcements, match reports, and other news content.

#### Fields:

| Field Name | Type | Description | Validation |
|------------|------|-------------|------------|
| `title` | String | Article headline | Required |
| `slug` | Slug | URL-friendly identifier, auto-generated from title | Required |
| `publishedAt` | DateTime | Publication date and time | Required, defaults to current time |
| `mainImage` | Image | Featured image for the article | Required, alt text required |
| `summary` | Text | Brief summary (max 200 characters) | Optional |
| `body` | Block content | Main article content with rich text and images | Required, images require alt text |

#### Preview Configuration:

The news article preview in the Sanity Studio shows:
- Title
- Publication date
- Main image

### Sponsor (`sponsor`)

Sponsors represent organizations or individuals that support the club.

#### Fields:

| Field Name | Type | Description | Validation |
|------------|------|-------------|------------|
| `name` | String | Sponsor name | Required |
| `logo` | Image | Sponsor logo | Required, alt text required |
| `websiteUrl` | URL | Link to sponsor website | Optional, must be valid http/https URL |
| `sponsorshipLevel` | String | Level of sponsorship (Platinum, Gold, Silver, Bronze, Other) | Default: 'Other' |
| `displayOrder` | Number | Order for display (lower numbers first) | Optional |
| `description` | Text | Short description of the sponsor | Optional |

#### Preview Configuration:

The sponsor preview in the Sanity Studio shows:
- Sponsor name
- Sponsorship level
- Logo

#### Ordering Options:

- By display order (ascending)
- By name (alphabetical)

### Gallery Image (`galleryImage`)

Gallery images are used for the photo gallery section of the website.

#### Fields:

| Field Name | Type | Description | Validation |
|------------|------|-------------|------------|
| `title` | String | Image title or caption | Required |
| `imageFile` | Image | The actual image file | Required, alt text required |
| `dateTaken` | Date | When the photo was taken | Optional |
| `photographer` | String | Name of the photographer | Optional |
| `category` | String | Category (match, training, team-event, community, other) | Default: 'other' |

#### Preview Configuration:

The gallery image preview in the Sanity Studio shows:
- Title/caption
- Thumbnail of the image
- Date taken (if available)

### Player (`player`)

Players represent team members with their profiles and statistics.

#### Fields:

| Field Name | Type | Description | Validation |
|------------|------|-------------|------------|
| `name` | String | Player's full name | Required |
| `slug` | Slug | URL-friendly identifier, auto-generated from name | Required |
| `position` | String | Main position category (Goalkeeper, Defender, Midfielder, Forward) | Required |
| `specificPosition` | String | Detailed playing position (e.g., Centre-Back, Striker, etc.) | Required |
| `role` | String | Team role (Player, Captain, Vice Captain) | Required |
| `jerseyNumber` | Number | Jersey/shirt number | Required, integer 1-99 |
| `image` | Image | Player's photo | Optional, alt text required if provided |
| `stats` | Object | Player statistics | - |
| `stats.appearances` | Number | Number of appearances | Default: 0, min: 0 |
| `stats.goals` | Number | Goals scored | Default: 0, min: 0, hidden for goalkeepers |
| `stats.assists` | Number | Assists provided | Default: 0, min: 0, hidden for goalkeepers |
| `stats.cleanSheets` | Number | Clean sheets (for goalkeepers) | Default: 0, min: 0, only for goalkeepers |
| `bio` | Text | Player biography | Optional |
| `isActive` | Boolean | Whether player is active | Default: true |

#### Position System:

The player schema now supports a two-tier position system:

**Main Position Categories** (for team organization):
- Goalkeeper
- Defender
- Midfielder
- Forward

**Specific Positions** (detailed roles):

**Goalkeeper:**
- Goalkeeper

**Defender:**
- Centre-Back (CB)
- Left-Back (LB)
- Right-Back (RB)
- Wing-Back (WB)
- Sweeper

**Midfielder:**
- Defensive Midfielder (CDM)
- Central Midfielder (CM)
- Attacking Midfielder (CAM)
- Left Midfielder (LM)
- Right Midfielder (RM)
- Left Winger (LW)
- Right Winger (RW)
- Box-to-Box Midfielder

**Forward:**
- Striker (ST)
- Centre-Forward (CF)
- Left Forward (LF)
- Right Forward (RF)
- Second Striker (SS)
- False 9

#### Preview Configuration

The player preview in the Sanity Studio shows:

- Role prefix and jersey number with name (e.g., "(C) #10 John Smith" for Captain)
- Specific position (falls back to main position if not set)
- Player photo

### Staff (`staff`)

Staff represents coaching and management personnel.

#### Fields:

| Field Name | Type | Description | Validation |
|------------|------|-------------|------------|
| `name` | String | Staff member's full name | Required |
| `slug` | Slug | URL-friendly identifier, auto-generated from name | Required |
| `role` | String | Role in the team (Head Coach, Assistant Coach, etc.) | Required |
| `image` | Image | Staff member's photo | Required, alt text required |
| `bio` | Text | Staff biography | Optional |
| `contactInfo` | Object | Contact information | Optional |
| `contactInfo.email` | String | Email address | Optional, valid email format |
| `contactInfo.phone` | String | Phone number | Optional |
| `isActive` | Boolean | Whether staff member is active | Default: true |

#### Preview Configuration:

The staff preview in the Sanity Studio shows:
- Name
- Role
- Staff photo

## Using the Schemas

### For Content Editors

1. **Creating Content:**
   - Log in to the Sanity Studio
   - Select the content type you want to create
   - Fill in the required fields
   - Click "Publish" to make the content available on the website

2. **Editing Content:**
   - Find the content item in the appropriate section
   - Make your changes
   - Click "Publish" to update the content on the website

3. **Tips for Image Management:**
   - **Use the Hotspot Feature**: After uploading an image, use the hotspot tool to set the focal point
   - **Provide Meaningful Alt Text**: Describe the image content clearly for accessibility
   - **Optimize Before Uploading**: Resize and compress images before uploading for better performance
   - **Maintain Consistency**: Use consistent framing and style for player/staff photos

4. **Tips for Specific Content Types:**
   - **Events:** Be sure to select the correct event type as this affects which fields are shown
   - **News Articles:** The summary field is used on listing pages, so make it compelling
   - **Sponsors:** The display order field controls the order sponsors appear on the website
   - **Gallery Images:** Use descriptive titles and categorize properly for better organization
   - **Players/Staff:** Keep statistics and information up-to-date throughout the season

### For Developers

1. **Querying Content:**
   - Use GROQ queries to fetch content from Sanity
   - Example query for latest news:
     ```
     *[_type == "newsArticle"] | order(publishedAt desc)[0...3]
     ```
   - Example query for upcoming events:
     ```
     *[_type == "event" && date > now()] | order(date asc)[0...5]
     ```
   - Example query for active players by position:
     ```
     *[_type == "player" && isActive == true] | order(position asc, jerseyNumber asc)
     ```

2. **Extending Schemas:**
   - Add new fields to the schema files in the `schemaTypes` directory
   - Update the corresponding TypeScript interfaces in the frontend
   - Rebuild the Sanity Studio to reflect changes

## Best Practices

1. **Images:**
   - Use the hotspot feature to control image cropping
   - Always provide alt text for accessibility
   - Optimize images before uploading (recommended size: under 1MB)
   - Maintain consistent aspect ratios within content types

2. **Content Structure:**
   - Keep titles concise and descriptive
   - Use rich text formatting sparingly for better readability
   - Maintain consistent naming conventions across all content types

3. **Dates and Times:**
   - All dates and times are stored in ISO format
   - The frontend will handle formatting for display
   - Be mindful of timezone considerations

4. **Team Management:**
   - Update player statistics regularly
   - Mark players/staff as inactive rather than deleting them when they leave
   - Ensure consistent photo style for a professional appearance
