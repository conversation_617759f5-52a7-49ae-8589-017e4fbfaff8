# Troubleshooting Guide

This guide helps resolve common issues with the enhanced Sanity Studio.

## 🚨 **Common Errors & Solutions**

### 1. Import Errors

#### Error: `useToast is not exported from 'sanity'`
**Solution**: `useToast` should be imported from `@sanity/ui`, not `sanity`.

```javascript
// ❌ Wrong
import { useToast } from 'sanity'

// ✅ Correct
import { useToast } from '@sanity/ui'
```

#### Error: `Module not found: Can't resolve './plugins/...`
**Solution**: Ensure all plugin files exist and paths are correct.

1. Check file exists: `plugins/dashboard/DashboardTool.jsx`
2. Verify import paths in `sanity.config.js`
3. Restart the development server

### 2. Plugin Loading Issues

#### Error: `Plugin 'dashboard' not found`
**Solution**: 
1. Check plugin is properly exported in `plugins/dashboard/index.ts`
2. Verify plugin is imported in `sanity.config.js`
3. Restart Sanity Studio: `npm run dev`

#### Error: `Component not rendering`
**Solution**:
1. Check browser console for JavaScript errors
2. Verify all imports are correct
3. Check component syntax and JSX structure

### 3. Data Fetching Issues

#### Error: `GROQ query failed`
**Solution**:
1. Test queries in Vision tool (`/vision`)
2. Check field names match schema
3. Verify document types exist

#### Error: `Client fetch failed`
**Solution**:
1. Check Sanity project ID and dataset in config
2. Verify API permissions
3. Check network connectivity

### 4. CSV Import Issues

#### Error: `Duplicate players not being detected`
**Solution**:
1. Ensure "Prevent duplicate players" is checked
2. Check player names and jersey numbers for exact matches
3. Verify duplicate detection logic in CSV import tool

#### Error: `CSV parsing failed`
**Solution**:
1. Check CSV format (comma-separated, UTF-8 encoding)
2. Ensure required columns exist (name, position)
3. Remove special characters from CSV data

### 5. Navigation Issues

#### Error: `Quick actions not working`
**Solution**:
1. Check browser console for navigation errors
2. Verify Sanity Studio URLs are correct
3. Try refreshing the page

#### Error: `Desk structure not loading`
**Solution**:
1. Check `deskStructure.ts` for syntax errors
2. Verify all imports are correct
3. Restart development server

## 🔧 **Development Issues**

### TypeScript Errors

#### Error: `Property does not exist on type`
**Solution**:
1. Check TypeScript interfaces match Sanity schema
2. Add proper type definitions
3. Use optional chaining (`?.`) for optional properties

#### Error: `Cannot find module`
**Solution**:
1. Install missing dependencies: `npm install`
2. Check import paths are correct
3. Verify file extensions (.ts, .tsx, .js, .jsx)

### Build Issues

#### Error: `Build failed with errors`
**Solution**:
1. Check all files for syntax errors
2. Verify all imports are resolved
3. Run `npm run build` to see detailed errors

#### Error: `Memory issues during build`
**Solution**:
1. Increase Node.js memory: `NODE_OPTIONS="--max-old-space-size=4096"`
2. Clear node_modules and reinstall
3. Check for circular dependencies

## 🛠️ **Performance Issues**

### Slow Dashboard Loading

**Solution**:
1. Optimize GROQ queries (use projections)
2. Limit data fetching (use pagination)
3. Add loading states and error handling

### Large CSV Import Timeouts

**Solution**:
1. Split large CSV files into smaller batches
2. Increase timeout settings
3. Use command-line import for very large datasets

## 📋 **Data Issues**

### Missing Content

**Solution**:
1. Check content is published (has `publishedAt` date)
2. Verify schema field names
3. Check GROQ query filters

### Incorrect Statistics

**Solution**:
1. Refresh dashboard data
2. Check GROQ count queries
3. Verify document types and filters

## 🔄 **Reset & Recovery**

### Reset Development Environment

```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Restart development server
npm run dev
```

### Reset Sanity Studio

```bash
# Clear Sanity cache
npx sanity exec --with-user-token scripts/clear-cache.js

# Rebuild studio
npm run build
```

### Backup & Restore Data

```bash
# Export all data
SANITY_PROJECT_ID=your-id SANITY_TOKEN=your-token node scripts/manage-players.js list > backup.json

# Import data (use CSV import tool)
```

## 📞 **Getting Help**

### Debug Information to Collect

1. **Error messages** (full stack trace)
2. **Browser console logs**
3. **Sanity Studio version**: Check `package.json`
4. **Node.js version**: `node --version`
5. **Operating system**

### Useful Commands

```bash
# Check Sanity CLI version
npx sanity --version

# Validate schema
npx sanity schema validate

# Check project status
npx sanity projects list

# View logs
npx sanity logs
```

### Resources

- [Sanity Documentation](https://www.sanity.io/docs)
- [Sanity Community](https://www.sanity.io/community)
- [GitHub Issues](https://github.com/sanity-io/sanity/issues)

---

*If you encounter issues not covered here, check the browser console for detailed error messages and refer to the Sanity documentation.*
