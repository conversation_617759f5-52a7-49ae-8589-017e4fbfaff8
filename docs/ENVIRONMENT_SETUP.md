# Environment Setup Guide

## Environment Variables Configuration

### Required Environment Variables

Create a `.env.local` file in the `frontend/` directory with the following variables:

```bash
# Sanity CMS Configuration
NEXT_PUBLIC_SANITY_PROJECT_ID="9at30otk"
NEXT_PUBLIC_SANITY_DATASET="production"
NEXT_PUBLIC_SANITY_API_VERSION="2024-05-23"

# Domain Configuration
NEXT_PUBLIC_BASE_URL="https://nnufc.com"

# Analytics Configuration
NEXT_PUBLIC_CLARITY_ID="rp28jewrfj"
# NEXT_PUBLIC_GA_MEASUREMENT_ID="" # Add Google Analytics ID when ready

# Optional: Enable analytics in development
# NEXT_PUBLIC_ENABLE_ANALYTICS="true"
```

### Environment-Specific Configurations

#### Development Environment
```bash
# Local development
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_ENABLE_ANALYTICS="false"  # Default: disabled in dev
```

#### Production Environment (Vercel)
```bash
# Production deployment
NEXT_PUBLIC_BASE_URL="https://nnufc.com"
NEXT_PUBLIC_CLARITY_ID="rp28jewrfj"
# Analytics automatically enabled in production
```

## Deployment Configuration

### Vercel Environment Variables

When deploying to Vercel, add these environment variables in the Vercel dashboard:

1. **Go to**: Vercel Dashboard → Your Project → Settings → Environment Variables

2. **Add Variables**:
   ```
   NEXT_PUBLIC_SANITY_PROJECT_ID = 9at30otk
   NEXT_PUBLIC_SANITY_DATASET = production
   NEXT_PUBLIC_SANITY_API_VERSION = 2024-05-23
   NEXT_PUBLIC_BASE_URL = https://nnufc.com
   NEXT_PUBLIC_CLARITY_ID = rp28jewrfj
   ```

3. **Environment Scope**: Set to "Production", "Preview", and "Development" as needed

### Domain Configuration

#### Custom Domain Setup (Vercel)
1. **Add Domain**: In Vercel dashboard, go to Domains
2. **Add**: `nnufc.com` and `www.nnufc.com`
3. **DNS Configuration**: Update your domain registrar with Vercel's DNS settings
4. **SSL**: Automatic SSL certificate provisioning

#### DNS Records Example
```
Type: A
Name: @
Value: 76.76.19.61

Type: CNAME
Name: www
Value: cname.vercel-dns.com
```

## Analytics Setup

### Microsoft Clarity (Active)
- **Status**: ✅ Configured and Active
- **Project ID**: `rp28jewrfj`
- **Dashboard**: https://clarity.microsoft.com/
- **Features**: Heatmaps, session recordings, user behavior tracking

### Google Analytics (Ready for Setup)
When ready to add Google Analytics:

1. **Create GA4 Property**: 
   - Go to https://analytics.google.com/
   - Create new property for `nnufc.com`
   - Get Measurement ID (format: `G-XXXXXXXXXX`)

2. **Add to Environment**:
   ```bash
   NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"
   ```

3. **Deploy**: The analytics component will automatically include GA4

## Security Considerations

### Environment Variable Security
- **Never commit** `.env.local` to version control
- **Use NEXT_PUBLIC_** prefix only for client-side variables
- **Sensitive data** should use server-side environment variables

### .gitignore Configuration
Ensure your `.gitignore` includes:
```
# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
```

## Testing Configuration

### Development Testing
```bash
# Test with analytics enabled in development
NEXT_PUBLIC_ENABLE_ANALYTICS="true" npm run dev
```

### Production Testing
```bash
# Build and test production bundle
npm run build
npm run start
```

### Analytics Testing
1. **Open Browser Dev Tools**
2. **Check Console**: Look for analytics events in development mode
3. **Network Tab**: Verify analytics scripts are loading
4. **Clarity Dashboard**: Check real-time data in production

## Troubleshooting

### Common Issues

#### Analytics Not Loading
```bash
# Check environment variables
echo $NEXT_PUBLIC_CLARITY_ID
echo $NEXT_PUBLIC_BASE_URL

# Verify in browser console
console.log(process.env.NEXT_PUBLIC_CLARITY_ID)
```

#### Domain Issues
- Verify DNS propagation: `nslookup nnufc.com`
- Check SSL certificate: Browser should show secure connection
- Test redirects: `www.nnufc.com` should redirect to `nnufc.com`

#### Build Errors
```bash
# Clear Next.js cache
rm -rf .next
npm run build

# Check environment variables in build
npm run build 2>&1 | grep -i env
```

### Debug Commands

#### Check Analytics Status
```javascript
// In browser console
console.log('Analytics Status:', {
  clarity: !!window.clarity,
  ga: !!window.gtag,
  environment: process.env.NODE_ENV
})
```

#### Test Analytics Events
```javascript
// Test Clarity
if (window.clarity) {
  window.clarity('event', 'test_event', { source: 'manual_test' })
}

// Test GA (when configured)
if (window.gtag) {
  window.gtag('event', 'test_event', { source: 'manual_test' })
}
```

## Performance Monitoring

### Core Web Vitals
Monitor these metrics in production:
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

### Analytics Impact
- **Script Loading**: Asynchronous, minimal impact
- **Bundle Size**: ~5KB additional JavaScript
- **Performance**: Monitor with Lighthouse and PageSpeed Insights

## Backup and Recovery

### Environment Variable Backup
Keep a secure backup of production environment variables:
```bash
# Example backup (DO NOT commit to repo)
# Store in secure password manager or encrypted file
PRODUCTION_ENV_BACKUP.txt
```

### Configuration Rollback
If issues occur:
1. **Revert Environment Variables**: Use previous working values
2. **Redeploy**: Trigger new deployment with corrected config
3. **Verify**: Test all functionality after rollback

## Future Enhancements

### Planned Additions
- **Google Analytics 4**: Enhanced tracking and insights
- **Tag Manager**: Centralized tag management
- **A/B Testing**: Experiment framework
- **Performance Monitoring**: Real User Monitoring (RUM)

### Integration Roadmap
1. **Phase 1**: Complete GA4 setup ✅ Ready
2. **Phase 2**: Advanced event tracking
3. **Phase 3**: Conversion funnel analysis
4. **Phase 4**: Automated reporting and alerts
