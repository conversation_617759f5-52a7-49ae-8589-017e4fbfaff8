# Enhanced Credentials Management System

This document explains how to use the improved credentials management system that supports multiple authentication methods including passwords, API keys, and OAuth.

## Authentication Methods Supported

### 1. **Password Authentication** (`auth_method = 'password'`)
Traditional username/password combinations.

**Required fields:**
- `username` - Login username/email
- `password` - Login password

**Example:**
```sql
SELECT add_credential(
    p_name := 'WordPress Admin',
    p_credential_type := 'CMS',
    p_auth_method := 'password',
    p_url := 'https://mysite.com/wp-admin',
    p_username := '<EMAIL>',
    p_password := 'secure_password_123',
    p_notes := 'WordPress admin access'
);
```

### 2. **API Key Authentication** (`auth_method = 'api_key'`)
For services that use API keys for authentication.

**Required fields:**
- `api_key` - The API key/token

**Optional fields:**
- `api_secret` - API secret (if required)
- `username` - Account identifier (optional)

**Examples:**
```sql
-- Simple API key (like Resend, Stripe)
SELECT add_credential(
    p_name := 'Resend Email API',
    p_credential_type := 'Email Service',
    p_auth_method := 'api_key',
    p_url := 'https://resend.com',
    p_api_key := 're_your_api_key_here',
    p_notes := 'Transactional email service'
);

-- API key with secret (like AWS, some payment processors)
SELECT add_credential(
    p_name := 'AWS S3',
    p_credential_type := 'Cloud Storage',
    p_auth_method := 'api_key',
    p_url := 'https://aws.amazon.com',
    p_api_key := 'AKIA...',
    p_api_secret := 'your_secret_key',
    p_notes := 'AWS S3 bucket access'
);
```

### 3. **Google OAuth** (`auth_method = 'oauth_google'`)
For services authenticated through Google accounts.

**Required fields:**
- `username` - Google account email

**Optional fields:**
- `oauth_scopes` - Specific permissions granted
- `oauth_client_id` - If using custom OAuth app

**Example:**
```sql
SELECT add_credential(
    p_name := 'Google Analytics',
    p_credential_type := 'Analytics',
    p_auth_method := 'oauth_google',
    p_url := 'https://analytics.google.com',
    p_username := '<EMAIL>',
    p_oauth_scopes := 'analytics.readonly',
    p_notes := 'No password needed - uses Google OAuth'
);
```

### 4. **Other OAuth Providers** (`auth_method = 'oauth_github'`, `oauth_microsoft`, `oauth_other`)
For GitHub, Microsoft, or other OAuth providers.

**Examples:**
```sql
-- GitHub OAuth
SELECT add_credential(
    p_name := 'GitHub Repository',
    p_credential_type := 'Development',
    p_auth_method := 'oauth_github',
    p_url := 'https://github.com/myorg/myrepo',
    p_username := 'github_username',
    p_notes := 'Repository access via GitHub OAuth'
);

-- Custom OAuth provider
SELECT add_credential(
    p_name := 'Custom Service',
    p_credential_type := 'API',
    p_auth_method := 'oauth_other',
    p_oauth_provider := 'custom_provider',
    p_url := 'https://api.customservice.com',
    p_username := '<EMAIL>',
    p_notes := 'Custom OAuth integration'
);
```

### 5. **Mixed Authentication** (`auth_method = 'mixed'`)
For services that use multiple authentication methods.

**Example:**
```sql
SELECT add_credential(
    p_name := 'GitHub Enterprise',
    p_credential_type := 'Development',
    p_auth_method := 'mixed',
    p_url := 'https://github.com',
    p_username := 'github_username',
    p_api_key := 'ghp_personal_access_token',
    p_oauth_provider := 'github',
    p_notes := 'OAuth for web, PAT for API access'
);
```

## Additional Features

### Environment Support
Specify which environment the credential is for:
- `production` (default)
- `staging`
- `development`
- `testing`

### Verification Status
Track whether credentials are working:
- `not_tested` (default)
- `verified`
- `failed`
- `pending`

### Expiry Tracking
Set expiry dates for credentials that expire:
```sql
SELECT add_credential(
    p_name := 'SSL Certificate',
    p_credential_type := 'Security',
    p_auth_method := 'api_key',
    p_api_key := 'cert_key_here',
    p_expiry_date := '2024-12-31',
    p_notes := 'SSL certificate expires annually'
);
```

## Querying Credentials

### View All Credentials
```sql
SELECT * FROM credentials_summary ORDER BY name;
```

### Filter by Authentication Method
```sql
SELECT * FROM credentials WHERE auth_method = 'oauth_google';
```

### Find Expiring Credentials
```sql
SELECT name, expiry_date 
FROM credentials 
WHERE expiry_date <= CURRENT_DATE + INTERVAL '30 days'
AND expiry_date IS NOT NULL;
```

### Check API Key Credentials
```sql
SELECT name, credential_type, url, notes
FROM credentials 
WHERE auth_method = 'api_key' 
AND is_active = true;
```

## Best Practices

1. **Use appropriate auth_method** - Don't store passwords for OAuth-only services
2. **Set expiry dates** - For credentials that expire (certificates, tokens)
3. **Use environments** - Separate production from development credentials
4. **Add detailed notes** - Include recovery information, special instructions
5. **Mark inactive credentials** - Set `is_active = false` instead of deleting
6. **Regular verification** - Update `verification_status` and `last_verified_at`

## Security Notes

- Passwords and API keys are stored in plain text - consider encryption for production
- Use environment variables for sensitive credentials in applications
- Regularly rotate API keys and update expiry dates
- Review and clean up unused credentials periodically
