/* eslint-env node */
/**
 * Import Players from CSV to Sanity
 *
 * This script imports player data from a CSV file into Sanity CMS.
 * It handles player details including name, position, stats, and more.
 */

const csv = require('csv-parser');
const fs = require('fs');
const { createClient } = require('@sanity/client');
const path = require('path');

// Configure Sanity client
const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID,
  dataset: process.env.SANITY_DATASET || 'production',
  token: process.env.SANITY_TOKEN, // API token with write access
  useCdn: false, // We need to use the API directly for writes
  apiVersion: '2021-10-21'
});

// Default image placeholder - will be used if no image URL is provided
const DEFAULT_PLAYER_IMAGE = './frontend/public/placeholder-player.svg';

/**
 * Generate a slug from a string
 * @param {string} input - Text to convert to slug
 * @returns {string} - URL-safe slug
 */
function generateSlug(input) {
  return input
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-'); // Remove consecutive hyphens
}

/**
 * Process and import player data from CSV
 * @param {string} csvFilePath - Path to the CSV file
 */
async function importPlayers(csvFilePath) {
  if (!fs.existsSync(csvFilePath)) {
    console.error(`Error: CSV file not found at ${csvFilePath}`);
    process.exit(1);
  }

  console.log(`Starting import from ${csvFilePath}...`);
  const results = [];

  // Parse CSV file
  fs.createReadStream(csvFilePath)
    .pipe(csv())
    .on('data', (data) => results.push(data))
    .on('end', async () => {
      console.log(`Found ${results.length} players to import.`);

      let successCount = 0;
      let errorCount = 0;

      for (const [index, player] of results.entries()) {
        try {
          console.log(`Processing player ${index + 1}/${results.length}: ${player.name}`);

          // Skip if name is missing
          if (!player.name) {
            console.error(`  Error: Missing player name, skipping record ${index + 1}`);
            errorCount++;
            continue;
          }

          // Skip if position is invalid
          const validPositions = ['Goalkeeper', 'Defender', 'Midfielder', 'Forward'];
          if (!validPositions.includes(player.position)) {
            console.error(`  Error: Invalid position "${player.position}" for ${player.name}, must be one of: ${validPositions.join(', ')}`);
            errorCount++;
            continue;
          }

          // Validate role if provided
          const validRoles = ['Player', 'Captain', 'Vice Captain'];
          const role = player.role || 'Player';
          if (!validRoles.includes(role)) {
            console.error(`  Error: Invalid role "${role}" for ${player.name}, must be one of: ${validRoles.join(', ')}`);
            errorCount++;
            continue;
          }

          // Prepare document for Sanity
          const doc = {
            _type: 'player',
            name: player.name,
            slug: {
              _type: 'slug',
              current: player.slug || generateSlug(player.name)
            },
            position: player.position,
            specificPosition: player.specificPosition || player.position, // Use specificPosition if provided, otherwise fall back to main position
            role: role,
            jerseyNumber: player.jerseyNumber ? parseInt(player.jerseyNumber, 10) : 0,
            bio: player.bio || '',
            isActive: player.isActive !== 'false', // Default to active if not specified
            stats: {
              appearances: player.appearances ? parseInt(player.appearances, 10) : 0,
              goals: player.position !== 'Goalkeeper' ? (player.goals ? parseInt(player.goals, 10) : 0) : undefined,
              assists: player.position !== 'Goalkeeper' ? (player.assists ? parseInt(player.assists, 10) : 0) : undefined,
              cleanSheets: player.position === 'Goalkeeper' ? (player.cleanSheets ? parseInt(player.cleanSheets, 10) : 0) : undefined
            }
          };

          // If player has an image URL, we would handle it here
          // For now, we can't automatically upload images via this script
          // That would require additional image processing and asset handling

          // Create document in Sanity
          const createdPlayer = await client.create(doc);
          console.log(`  Success: Created player ${createdPlayer._id} - ${player.name}`);
          successCount++;
        } catch (error) {
          console.error(`  Error importing ${player.name}: ${error.message}`);
          errorCount++;
        }
      }

      console.log('\nImport Summary:');
      console.log(`Total Players: ${results.length}`);
      console.log(`Successfully Imported: ${successCount}`);
      console.log(`Failed Imports: ${errorCount}`);
    });
}

// Check command line arguments
if (process.argv.length < 3) {
  console.log('Usage: node import-players.js <path-to-csv-file>');
  console.log('');
  console.log('Environment variables required:');
  console.log('  - SANITY_PROJECT_ID: Your Sanity project ID');
  console.log('  - SANITY_DATASET: Your dataset name (defaults to "production")');
  console.log('  - SANITY_TOKEN: A token with write permission');
  console.log('');
  console.log('Example:');
  console.log('  SANITY_PROJECT_ID=abc123 SANITY_TOKEN=sk... node import-players.js players.csv');
  process.exit(1);
}

// Run the import
const csvFilePath = process.argv[2];
importPlayers(csvFilePath);