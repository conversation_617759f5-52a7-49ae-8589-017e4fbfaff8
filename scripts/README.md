# Brisbane FC Import Scripts

This directory contains utility scripts for importing data into the Brisbane FC website's Sanity CMS.

## Available Scripts

- **import-players.js** - Import player data from a CSV file
- **import-staff.js** - Import staff data from a CSV file
- **manage-players.js** - Manage existing player data (delete all, remove duplicates, list players)

## Prerequisites

Before using these scripts, you'll need to:

1. Install required dependencies:

```bash
npm install csv-parser @sanity/client
```

2. Have a Sanity API token with write permissions:
   - Log into your Sanity account
   - Go to [https://www.sanity.io/manage](https://www.sanity.io/manage)
   - Select your project
   - Navigate to API > Tokens
   - Create a new token with "Editor" permissions
   - Copy the token for use with the scripts

## Usage Instructions

### Importing Players

The player import script expects a CSV file with the following columns:
- `name` (required) - Player's full name
- `position` (required) - Must be one of: Goalkeeper, Defender, Midfielder, Forward
- `role` - Team role: Player, Captain, or Vice Captain (defaults to "Player" if not specified)
- `jerseyNumber` - Player's jersey number
- `bio` - Brief player biography
- `isActive` - Set to "false" for inactive players (defaults to true if not specified)
- `appearances` - Number of appearances
- `goals` - Number of goals (for non-goalkeepers)
- `cleanSheets` - Number of clean sheets (for goalkeepers)
- `assists` - Number of assists (for non-goalkeepers)

Run the script:

```bash
SANITY_PROJECT_ID=your-project-id SANITY_TOKEN=your-token node scripts/import-players.js scripts/samples/players-template.csv
```

### Importing Staff

The staff import script expects a CSV file with the following columns:
- `name` (required) - Staff member's full name
- `role` - Staff role (e.g., Head Coach, Assistant Coach, Team Manager, etc.)
- `bio` - Brief staff biography
- `isActive` - Set to "false" for inactive staff (defaults to true if not specified)
- `email` - Contact email address
- `phone` - Contact phone number

Run the script:

```bash
SANITY_PROJECT_ID=your-project-id SANITY_TOKEN=your-token node scripts/import-staff.js scripts/samples/staff-template.csv
```

### Managing Players

The player management script provides utilities for managing existing player data:

**List all players:**
```bash
SANITY_PROJECT_ID=your-project-id SANITY_TOKEN=your-token node scripts/manage-players.js list
```

**Remove duplicate players (keeps the oldest of each duplicate group):**
```bash
SANITY_PROJECT_ID=your-project-id SANITY_TOKEN=your-token node scripts/manage-players.js remove-duplicates
```

**Delete all players (with confirmation prompt):**
```bash
SANITY_PROJECT_ID=your-project-id SANITY_TOKEN=your-token node scripts/manage-players.js delete-all
```

## Sample Templates

Example CSV templates are provided in the `samples` directory:
- `players-template.csv` - Template for player imports
- `staff-template.csv` - Template for staff imports

You can copy and modify these templates to match your data.

## Notes

- The scripts perform basic validation on required fields
- Images are not automatically imported via these scripts
- You'll need to add player/staff images manually through the Sanity Studio interface