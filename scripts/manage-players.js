/* eslint-env node */
/**
 * Player Management Script for Sanity
 *
 * This script provides utilities for managing player data in Sanity CMS:
 * - Delete all players
 * - Find and remove duplicate players
 * - List all players with basic info
 */

const { createClient } = require('@sanity/client');

// Configure Sanity client
const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID,
  dataset: process.env.SANITY_DATASET || 'production',
  token: process.env.SANITY_TOKEN, // API token with write access
  useCdn: false, // We need to use the API directly for writes
  apiVersion: '2021-10-21'
});

/**
 * Delete all players from Sanity
 */
async function deleteAllPlayers() {
  console.log('🗑️  Fetching all players...');
  
  try {
    // Fetch all player IDs
    const playerIds = await client.fetch(`*[_type == "player"]._id`);
    
    if (playerIds.length === 0) {
      console.log('ℹ️  No players found to delete.');
      return;
    }

    console.log(`📊 Found ${playerIds.length} players to delete.`);
    
    // Confirm deletion
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question(`⚠️  Are you sure you want to delete ALL ${playerIds.length} players? This cannot be undone. (yes/no): `, resolve);
    });
    rl.close();

    if (answer.toLowerCase() !== 'yes') {
      console.log('❌ Operation cancelled.');
      return;
    }

    // Delete all players in batches
    const batchSize = 100;
    let deletedCount = 0;
    
    for (let i = 0; i < playerIds.length; i += batchSize) {
      const batch = playerIds.slice(i, i + batchSize);
      await client.delete(batch);
      deletedCount += batch.length;
      console.log(`🗑️  Deleted ${deletedCount}/${playerIds.length} players...`);
    }

    console.log(`✅ Successfully deleted ${deletedCount} players.`);
  } catch (error) {
    console.error('❌ Error deleting players:', error.message);
    process.exit(1);
  }
}

/**
 * Find and remove duplicate players
 */
async function removeDuplicatePlayers() {
  console.log('🔍 Finding duplicate players...');
  
  try {
    // Fetch all players with name and jersey number
    const players = await client.fetch(`
      *[_type == "player"] {
        _id,
        name,
        jerseyNumber,
        position,
        _createdAt
      } | order(_createdAt asc)
    `);

    if (players.length === 0) {
      console.log('ℹ️  No players found.');
      return;
    }

    console.log(`📊 Found ${players.length} total players.`);

    // Group players by name and jersey number to find duplicates
    const duplicateGroups = {};
    
    players.forEach(player => {
      const key = `${player.name}-${player.jerseyNumber}`;
      if (!duplicateGroups[key]) {
        duplicateGroups[key] = [];
      }
      duplicateGroups[key].push(player);
    });

    // Find groups with more than one player (duplicates)
    const duplicates = Object.values(duplicateGroups).filter(group => group.length > 1);
    
    if (duplicates.length === 0) {
      console.log('✅ No duplicate players found.');
      return;
    }

    console.log(`⚠️  Found ${duplicates.length} groups of duplicate players:`);
    
    let toDelete = [];
    duplicates.forEach((group, index) => {
      console.log(`\n📋 Group ${index + 1}: ${group[0].name} (Jersey #${group[0].jerseyNumber})`);
      group.forEach((player, playerIndex) => {
        const status = playerIndex === 0 ? '(KEEPING - oldest)' : '(WILL DELETE)';
        console.log(`   ${playerIndex + 1}. ID: ${player._id}, Created: ${player._createdAt} ${status}`);
        if (playerIndex > 0) {
          toDelete.push(player._id);
        }
      });
    });

    if (toDelete.length === 0) {
      console.log('ℹ️  No duplicates to remove.');
      return;
    }

    console.log(`\n🗑️  Will delete ${toDelete.length} duplicate players (keeping the oldest of each group).`);
    
    // Confirm deletion
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question(`⚠️  Proceed with deleting ${toDelete.length} duplicate players? (yes/no): `, resolve);
    });
    rl.close();

    if (answer.toLowerCase() !== 'yes') {
      console.log('❌ Operation cancelled.');
      return;
    }

    // Delete duplicates in batches
    const batchSize = 100;
    let deletedCount = 0;
    
    for (let i = 0; i < toDelete.length; i += batchSize) {
      const batch = toDelete.slice(i, i + batchSize);
      await client.delete(batch);
      deletedCount += batch.length;
      console.log(`🗑️  Deleted ${deletedCount}/${toDelete.length} duplicate players...`);
    }

    console.log(`✅ Successfully removed ${deletedCount} duplicate players.`);
  } catch (error) {
    console.error('❌ Error removing duplicates:', error.message);
    process.exit(1);
  }
}

/**
 * List all players with basic information
 */
async function listPlayers() {
  console.log('📋 Listing all players...');
  
  try {
    const players = await client.fetch(`
      *[_type == "player"] {
        _id,
        name,
        position,
        jerseyNumber,
        role,
        isActive,
        _createdAt
      } | order(position asc, jerseyNumber asc)
    `);

    if (players.length === 0) {
      console.log('ℹ️  No players found.');
      return;
    }

    console.log(`📊 Found ${players.length} players:\n`);
    
    let currentPosition = '';
    players.forEach((player, index) => {
      if (player.position !== currentPosition) {
        currentPosition = player.position;
        console.log(`\n🏃 ${currentPosition.toUpperCase()}S:`);
      }
      
      const status = player.isActive ? '✅' : '❌';
      const role = player.role !== 'Player' ? ` (${player.role})` : '';
      console.log(`   ${status} #${player.jerseyNumber || '?'} ${player.name}${role}`);
    });

    console.log(`\n📊 Total: ${players.length} players`);
  } catch (error) {
    console.error('❌ Error listing players:', error.message);
    process.exit(1);
  }
}

// Main function to handle command line arguments
async function main() {
  const command = process.argv[2];

  // Check for required environment variables
  if (!process.env.SANITY_PROJECT_ID || !process.env.SANITY_TOKEN) {
    console.error('❌ Missing required environment variables:');
    console.error('   SANITY_PROJECT_ID - Your Sanity project ID');
    console.error('   SANITY_TOKEN - API token with write permissions');
    console.error('   SANITY_DATASET - Dataset name (optional, defaults to "production")');
    process.exit(1);
  }

  switch (command) {
    case 'delete-all':
      await deleteAllPlayers();
      break;
    case 'remove-duplicates':
      await removeDuplicatePlayers();
      break;
    case 'list':
      await listPlayers();
      break;
    default:
      console.log('🛠️  Player Management Script');
      console.log('');
      console.log('Usage: node scripts/manage-players.js <command>');
      console.log('');
      console.log('Commands:');
      console.log('  delete-all        Delete all players from Sanity');
      console.log('  remove-duplicates Find and remove duplicate players');
      console.log('  list             List all players with basic info');
      console.log('');
      console.log('Environment variables required:');
      console.log('  SANITY_PROJECT_ID - Your Sanity project ID');
      console.log('  SANITY_TOKEN      - API token with write permissions');
      console.log('  SANITY_DATASET    - Dataset name (defaults to "production")');
      console.log('');
      console.log('Examples:');
      console.log('  SANITY_PROJECT_ID=abc123 SANITY_TOKEN=sk... node scripts/manage-players.js list');
      console.log('  SANITY_PROJECT_ID=abc123 SANITY_TOKEN=sk... node scripts/manage-players.js delete-all');
      process.exit(1);
  }
}

// Run the script
main().catch(console.error);
