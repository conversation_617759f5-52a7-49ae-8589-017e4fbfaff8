/* eslint-env node */
/**
 * Debug Players Script
 * 
 * This script helps debug player data in Sanity to understand
 * what's causing duplicate detection issues.
 */

const { createClient } = require('@sanity/client');

// Configure Sanity client
const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID || '9at30otk',
  dataset: process.env.SANITY_DATASET || 'production',
  token: process.env.SANITY_TOKEN,
  useCdn: false,
  apiVersion: '2021-10-21'
});

async function debugPlayers() {
  try {
    console.log('🔍 Debugging player data in Sanity...\n');

    // 1. Count total players
    const totalPlayers = await client.fetch(`count(*[_type == "player"])`);
    console.log(`📊 Total players in database: ${totalPlayers}`);

    // 2. List all players with basic info
    const allPlayers = await client.fetch(`
      *[_type == "player"] | order(_createdAt desc) {
        _id,
        name,
        jerseyNumber,
        position,
        isActive,
        _createdAt,
        _updatedAt
      }
    `);

    if (allPlayers.length === 0) {
      console.log('✅ No players found in database - it\'s clean!');
      return;
    }

    console.log('\n📋 All players in database:');
    console.log('=' .repeat(80));
    
    allPlayers.forEach((player, index) => {
      const createdDate = new Date(player._createdAt).toLocaleString();
      const updatedDate = new Date(player._updatedAt).toLocaleString();
      
      console.log(`${index + 1}. ${player.name}`);
      console.log(`   ID: ${player._id}`);
      console.log(`   Jersey: #${player.jerseyNumber || 'N/A'}`);
      console.log(`   Position: ${player.position || 'N/A'}`);
      console.log(`   Active: ${player.isActive ? 'Yes' : 'No'}`);
      console.log(`   Created: ${createdDate}`);
      console.log(`   Updated: ${updatedDate}`);
      console.log('');
    });

    // 3. Check for specific players that were reported as duplicates
    const suspiciousNames = [
      'Niraj Gurung',
      'Dipen Sunuwar', 
      'Paritosh Lal Shrestha',
      'Raj Basnet',
      'Nishan Gharti Magar'
    ];

    console.log('\n🔍 Checking for specific "duplicate" players:');
    console.log('=' .repeat(50));

    for (const name of suspiciousNames) {
      const matches = await client.fetch(`
        *[_type == "player" && name == $name] {
          _id,
          name,
          jerseyNumber,
          _createdAt
        }
      `, { name });

      if (matches.length > 0) {
        console.log(`❌ FOUND: "${name}" - ${matches.length} record(s)`);
        matches.forEach(match => {
          console.log(`   ID: ${match._id}`);
          console.log(`   Jersey: #${match.jerseyNumber || 'N/A'}`);
          console.log(`   Created: ${new Date(match._createdAt).toLocaleString()}`);
        });
      } else {
        console.log(`✅ NOT FOUND: "${name}"`);
      }
    }

    // 4. Check for jersey number conflicts
    console.log('\n🔢 Checking jersey number usage:');
    console.log('=' .repeat(40));
    
    const jerseyNumbers = await client.fetch(`
      *[_type == "player" && defined(jerseyNumber)] {
        name,
        jerseyNumber
      } | order(jerseyNumber asc)
    `);

    const jerseyMap = {};
    jerseyNumbers.forEach(player => {
      if (!jerseyMap[player.jerseyNumber]) {
        jerseyMap[player.jerseyNumber] = [];
      }
      jerseyMap[player.jerseyNumber].push(player.name);
    });

    Object.keys(jerseyMap).forEach(jersey => {
      const players = jerseyMap[jersey];
      if (players.length > 1) {
        console.log(`❌ Jersey #${jersey}: ${players.length} players - ${players.join(', ')}`);
      } else {
        console.log(`✅ Jersey #${jersey}: ${players[0]}`);
      }
    });

    // 5. Check recent activity
    console.log('\n⏰ Recent database activity (last 24 hours):');
    console.log('=' .repeat(50));
    
    const recentActivity = await client.fetch(`
      *[_type == "player" && _createdAt > dateTime(now()) - 86400] | order(_createdAt desc) {
        name,
        _createdAt,
        "action": "created"
      }
    `);

    const recentUpdates = await client.fetch(`
      *[_type == "player" && _updatedAt > dateTime(now()) - 86400 && _updatedAt != _createdAt] | order(_updatedAt desc) {
        name,
        _updatedAt,
        "action": "updated"
      }
    `);

    const allRecent = [...recentActivity, ...recentUpdates].sort((a, b) => 
      new Date(b._createdAt || b._updatedAt) - new Date(a._createdAt || a._updatedAt)
    );

    if (allRecent.length === 0) {
      console.log('No recent activity found.');
    } else {
      allRecent.forEach(activity => {
        const date = new Date(activity._createdAt || activity._updatedAt).toLocaleString();
        console.log(`${activity.action.toUpperCase()}: ${activity.name} at ${date}`);
      });
    }

  } catch (error) {
    console.error('❌ Error debugging players:', error.message);
    
    if (error.message.includes('Insufficient permissions')) {
      console.log('\n💡 Tip: Make sure you have a valid SANITY_TOKEN with read permissions');
      console.log('   You can create one at: https://www.sanity.io/manage');
    }
  }
}

// Check command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('🛠️  Player Debug Script');
  console.log('');
  console.log('Usage: node scripts/debug-players.js');
  console.log('');
  console.log('Environment variables:');
  console.log('  SANITY_PROJECT_ID - Your Sanity project ID (defaults to 9at30otk)');
  console.log('  SANITY_DATASET   - Dataset name (defaults to production)');
  console.log('  SANITY_TOKEN     - API token with read permissions');
  console.log('');
  console.log('Example:');
  console.log('  SANITY_TOKEN=sk... node scripts/debug-players.js');
  process.exit(0);
}

// Run the debug
debugPlayers();
