import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  <PERSON>lex,
  <PERSON>ack,
  Text,
  Grid,
  Badge,
  But<PERSON>,
  Spinner
} from '@sanity/ui'
import {
  UsersIcon,
  DocumentIcon,
  CalendarIcon,
  ImageIcon,
  TrendUpwardIcon,
  ClockIcon
} from '@sanity/icons'
import { useClient } from 'sanity'

export function DashboardTool() {
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState([])
  const client = useClient({ apiVersion: '2023-01-01' })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Fetch statistics
      const [
        playerCount,
        staffCount,
        newsCount,
        eventCount,
        sponsorCount,
        galleryCount,
        activePlayers,
        upcomingEvents,
        recentNews,
        recentActivity
      ] = await Promise.all([
        client.fetch(`count(*[_type == "player"])`),
        client.fetch(`count(*[_type == "staff"])`),
        client.fetch(`count(*[_type == "newsArticle"])`),
        client.fetch(`count(*[_type == "event"])`),
        client.fetch(`count(*[_type == "sponsor"])`),
        client.fetch(`count(*[_type == "galleryImage"])`),
        client.fetch(`count(*[_type == "player" && isActive == true])`),
        client.fetch(`count(*[_type == "event" && date > now()])`),
        client.fetch(`count(*[_type == "newsArticle" && publishedAt > dateTime(now()) - 86400*7])`),
        client.fetch(`
          *[_type in ["player", "staff", "newsArticle", "event"] && _createdAt > dateTime(now()) - 86400*7]
          | order(_createdAt desc)[0...10] {
            _type,
            _createdAt,
            name,
            title,
            "displayName": coalesce(name, title)
          }
        `)
      ])

      setStats({
        players: { total: playerCount, active: activePlayers },
        staff: { total: staffCount },
        news: { total: newsCount, recent: recentNews },
        events: { total: eventCount, upcoming: upcomingEvents },
        sponsors: { total: sponsorCount },
        gallery: { total: galleryCount }
      })

      setRecentActivity(recentActivity)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ title, value, subtitle, icon: Icon, color = 'primary' }) => (
    <Card padding={4} radius={2} shadow={1} tone={color}>
      <Flex align="center" gap={3}>
        <Box>
          <Icon style={{ fontSize: '24px' }} />
        </Box>
        <Stack space={1}>
          <Text size={3} weight="bold">{value}</Text>
          <Text size={1} weight="semibold">{title}</Text>
          {subtitle && <Text size={0} muted>{subtitle}</Text>}
        </Stack>
      </Flex>
    </Card>
  )

  const QuickAction = ({ title, description, onClick, icon: Icon }) => (
    <Card padding={3} radius={2} border>
      <Stack space={3}>
        <Flex align="center" gap={2}>
          <Icon style={{ fontSize: '18px' }} />
          <Text size={1} weight="semibold">{title}</Text>
        </Flex>
        <Text size={0} muted>{description}</Text>
        <Button text="Open" mode="ghost" onClick={onClick} />
      </Stack>
    </Card>
  )

  if (loading) {
    return (
      <Box padding={4}>
        <Flex align="center" justify="center" direction="column" padding={5}>
          <Spinner muted />
          <Box marginTop={3}>
            <Text>Loading dashboard...</Text>
          </Box>
        </Flex>
      </Box>
    )
  }

  return (
    <Box padding={4}>
      <Stack space={5}>
        {/* Header */}
        <Stack space={2}>
          <Text size={4} weight="bold">Northern Nepalese United FC Dashboard</Text>
          <Text size={1} muted>Overview of your club's content and recent activity</Text>
        </Stack>

        {/* Statistics Grid */}
        <Grid columns={[1, 2, 3]} gap={3}>
          <StatCard
            title="Players"
            value={stats?.players?.total || 0}
            subtitle={`${stats?.players?.active || 0} active`}
            icon={UsersIcon}
            color="positive"
          />
          <StatCard
            title="Staff Members"
            value={stats?.staff?.total || 0}
            icon={UsersIcon}
            color="primary"
          />
          <StatCard
            title="News Articles"
            value={stats?.news?.total || 0}
            subtitle={`${stats?.news?.recent || 0} this week`}
            icon={DocumentIcon}
            color="caution"
          />
          <StatCard
            title="Events"
            value={stats?.events?.total || 0}
            subtitle={`${stats?.events?.upcoming || 0} upcoming`}
            icon={CalendarIcon}
            color="critical"
          />
          <StatCard
            title="Sponsors"
            value={stats?.sponsors?.total || 0}
            icon={TrendUpwardIcon}
            color="positive"
          />
          <StatCard
            title="Gallery Images"
            value={stats?.gallery?.total || 0}
            icon={ImageIcon}
            color="primary"
          />
        </Grid>

        {/* Quick Actions */}
        <Stack space={3}>
          <Text size={2} weight="semibold">Quick Actions</Text>
          <Grid columns={[1, 2, 4]} gap={3}>
            <QuickAction
              title="Import Players"
              description="Upload player data from CSV"
              icon={UsersIcon}
              onClick={() => window.open('/tools/csv-import', '_self')}
            />
            <QuickAction
              title="Add News"
              description="Create a new news article"
              icon={DocumentIcon}
              onClick={() => window.open('/intent/create/template=newsArticle', '_self')}
            />
            <QuickAction
              title="Schedule Event"
              description="Add upcoming match or event"
              icon={CalendarIcon}
              onClick={() => window.open('/intent/create/template=event', '_self')}
            />
            <QuickAction
              title="Manage Gallery"
              description="Upload and organize photos"
              icon={ImageIcon}
              onClick={() => window.open('/desk/galleryImage', '_self')}
            />
          </Grid>
        </Stack>

        {/* Recent Activity */}
        <Stack space={3}>
          <Text size={2} weight="semibold">Recent Activity</Text>
          <Card padding={3} radius={2} border>
            <Stack space={3}>
              {recentActivity.length > 0 ? (
                recentActivity.map((item, index) => (
                  <Flex key={index} align="center" gap={3}>
                    <Badge tone="primary">{item._type}</Badge>
                    <Text size={1}>{item.displayName}</Text>
                    <Text size={0} muted>
                      {new Date(item._createdAt).toLocaleDateString()}
                    </Text>
                  </Flex>
                ))
              ) : (
                <Text size={1} muted>No recent activity</Text>
              )}
            </Stack>
          </Card>
        </Stack>

        {/* Refresh Button */}
        <Flex justify="center">
          <Button
            text="Refresh Dashboard"
            icon={ClockIcon}
            onClick={fetchDashboardData}
            mode="ghost"
          />
        </Flex>
      </Stack>
    </Box>
  )
}
