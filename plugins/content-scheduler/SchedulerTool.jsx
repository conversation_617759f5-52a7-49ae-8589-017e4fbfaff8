import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Card,
  <PERSON>lex,
  <PERSON>ack,
  Text,
  Grid,
  Badge,
  <PERSON><PERSON>,
  Spinner
} from '@sanity/ui'
import {
  CalendarIcon,
  ClockIcon,
  DocumentIcon,
  PublishIcon
} from '@sanity/icons'
import { useClient } from 'sanity'
import { useToast } from '@sanity/ui'

export function SchedulerTool() {
  const [loading, setLoading] = useState(true)
  const [scheduledContent, setScheduledContent] = useState([])
  const [draftContent, setDraftContent] = useState([])
  const client = useClient({ apiVersion: '2023-01-01' })
  const toast = useToast()

  useEffect(() => {
    fetchScheduledContent()
  }, [])

  const fetchScheduledContent = async () => {
    try {
      setLoading(true)

      // Fetch scheduled content (future publishedAt dates)
      const scheduled = await client.fetch(`
        *[_type in ["newsArticle", "event"] && publishedAt > now()]
        | order(publishedAt asc) {
          _id,
          _type,
          title,
          publishedAt,
          "status": "scheduled"
        }
      `)

      // Fetch draft content (no publishedAt date)
      const drafts = await client.fetch(`
        *[_type in ["newsArticle", "event"] && !defined(publishedAt)]
        | order(_createdAt desc) {
          _id,
          _type,
          title,
          _createdAt,
          "status": "draft"
        }
      `)

      setScheduledContent(scheduled)
      setDraftContent(drafts)
    } catch (error) {
      console.error('Error fetching scheduled content:', error)
      toast.push({
        status: 'error',
        title: 'Error',
        description: 'Failed to fetch scheduled content'
      })
    } finally {
      setLoading(false)
    }
  }

  const publishNow = async (documentId, documentType) => {
    try {
      await client
        .patch(documentId)
        .set({ publishedAt: new Date().toISOString() })
        .commit()

      toast.push({
        status: 'success',
        title: 'Published',
        description: 'Content published successfully'
      })

      // Refresh the data
      fetchScheduledContent()
    } catch (error) {
      toast.push({
        status: 'error',
        title: 'Error',
        description: 'Failed to publish content'
      })
    }
  }

  const unschedule = async (documentId) => {
    try {
      await client
        .patch(documentId)
        .unset(['publishedAt'])
        .commit()

      toast.push({
        status: 'success',
        title: 'Unscheduled',
        description: 'Content moved back to drafts'
      })

      // Refresh the data
      fetchScheduledContent()
    } catch (error) {
      toast.push({
        status: 'error',
        title: 'Error',
        description: 'Failed to unschedule content'
      })
    }
  }

  const ContentCard = ({ item, showActions = true }) => (
    <Card padding={3} border radius={2}>
      <Stack space={3}>
        <Flex justify="space-between" align="center">
          <Stack space={1}>
            <Text size={1} weight="semibold">{item.title}</Text>
            <Flex gap={2} align="center">
              <Badge tone={item._type === 'newsArticle' ? 'primary' : 'positive'}>
                {item._type === 'newsArticle' ? 'News' : 'Event'}
              </Badge>
              <Badge tone={item.status === 'scheduled' ? 'caution' : 'default'}>
                {item.status}
              </Badge>
            </Flex>
          </Stack>
          <DocumentIcon />
        </Flex>

        <Text size={0} muted>
          {item.status === 'scheduled'
            ? `Scheduled: ${new Date(item.publishedAt).toLocaleString()}`
            : `Created: ${new Date(item._createdAt).toLocaleString()}`
          }
        </Text>

        {showActions && (
          <Flex gap={2}>
            <Button
              text="Edit"
              mode="ghost"
              onClick={() => window.open(`/desk/${item._type};${item._id}`, '_self')}
            />
            {item.status === 'scheduled' ? (
              <>
                <Button
                  text="Publish Now"
                  tone="positive"
                  onClick={() => publishNow(item._id, item._type)}
                />
                <Button
                  text="Unschedule"
                  tone="caution"
                  onClick={() => unschedule(item._id)}
                />
              </>
            ) : (
              <Button
                text="Schedule"
                tone="primary"
                onClick={() => window.open(`/desk/${item._type};${item._id}`, '_self')}
              />
            )}
          </Flex>
        )}
      </Stack>
    </Card>
  )

  if (loading) {
    return (
      <Box padding={4}>
        <Flex align="center" justify="center" direction="column" padding={5}>
          <Spinner muted />
          <Box marginTop={3}>
            <Text>Loading scheduled content...</Text>
          </Box>
        </Flex>
      </Box>
    )
  }

  return (
    <Box padding={4}>
      <Stack space={5}>
        {/* Header */}
        <Stack space={2}>
          <Text size={4} weight="bold">Content Scheduler</Text>
          <Text size={1} muted>Manage scheduled and draft content</Text>
        </Stack>

        {/* Summary Cards */}
        <Grid columns={[1, 2, 4]} gap={3}>
          <Card padding={3} radius={2} shadow={1} tone="caution">
            <Flex align="center" gap={3}>
              <ClockIcon style={{ fontSize: '20px' }} />
              <Stack space={1}>
                <Text size={2} weight="bold">{scheduledContent.length}</Text>
                <Text size={0} weight="semibold">Scheduled</Text>
              </Stack>
            </Flex>
          </Card>

          <Card padding={3} radius={2} shadow={1} tone="default">
            <Flex align="center" gap={3}>
              <DocumentIcon style={{ fontSize: '20px' }} />
              <Stack space={1}>
                <Text size={2} weight="bold">{draftContent.length}</Text>
                <Text size={0} weight="semibold">Drafts</Text>
              </Stack>
            </Flex>
          </Card>

          <Card padding={3} radius={2} shadow={1} tone="positive">
            <Flex align="center" gap={3}>
              <PublishIcon style={{ fontSize: '20px' }} />
              <Stack space={1}>
                <Text size={2} weight="bold">
                  {scheduledContent.filter(item =>
                    new Date(item.publishedAt) <= new Date(Date.now() + 24 * 60 * 60 * 1000)
                  ).length}
                </Text>
                <Text size={0} weight="semibold">Due Today</Text>
              </Stack>
            </Flex>
          </Card>

          <Card padding={3} radius={2} shadow={1}>
            <Flex align="center" gap={3}>
              <CalendarIcon style={{ fontSize: '20px' }} />
              <Stack space={1}>
                <Text size={2} weight="bold">
                  {scheduledContent.length + draftContent.length}
                </Text>
                <Text size={0} weight="semibold">Total</Text>
              </Stack>
            </Flex>
          </Card>
        </Grid>

        <Grid columns={[1, 1, 2]} gap={4}>
          {/* Scheduled Content */}
          <Stack space={3}>
            <Flex justify="space-between" align="center">
              <Text size={2} weight="semibold">Scheduled Content</Text>
              <Button
                text="Refresh"
                mode="ghost"
                onClick={fetchScheduledContent}
              />
            </Flex>

            <Stack space={2}>
              {scheduledContent.length > 0 ? (
                scheduledContent.map((item) => (
                  <ContentCard key={item._id} item={item} />
                ))
              ) : (
                <Card padding={4} border radius={2}>
                  <Text size={1} muted>No scheduled content</Text>
                </Card>
              )}
            </Stack>
          </Stack>

          {/* Draft Content */}
          <Stack space={3}>
            <Text size={2} weight="semibold">Draft Content</Text>

            <Stack space={2}>
              {draftContent.length > 0 ? (
                draftContent.slice(0, 10).map((item) => (
                  <ContentCard key={item._id} item={item} />
                ))
              ) : (
                <Card padding={4} border radius={2}>
                  <Text size={1} muted>No draft content</Text>
                </Card>
              )}

              {draftContent.length > 10 && (
                <Card padding={2} border radius={2}>
                  <Text size={0} muted>
                    ...and {draftContent.length - 10} more drafts
                  </Text>
                </Card>
              )}
            </Stack>
          </Stack>
        </Grid>

        {/* Quick Actions */}
        <Stack space={3}>
          <Text size={2} weight="semibold">Quick Actions</Text>
          <Flex gap={3} wrap="wrap">
            <Button
              text="Create News Article"
              icon={DocumentIcon}
              onClick={() => window.open('/intent/create/template=newsArticle', '_self')}
            />
            <Button
              text="Schedule Event"
              icon={CalendarIcon}
              onClick={() => window.open('/intent/create/template=event', '_self')}
            />
            <Button
              text="View All Content"
              icon={DocumentIcon}
              mode="ghost"
              onClick={() => window.open('/desk', '_self')}
            />
          </Flex>
        </Stack>
      </Stack>
    </Box>
  )
}
