import React, { useState, useCallback } from 'react'
import { 
  Box, 
  Card, 
  Button, 
  Flex, 
  Stack, 
  Text, 
  Grid,
  Select,
  Checkbox,
  Badge,
  Spinner
} from '@sanity/ui'
import { DownloadIcon, DocumentIcon } from '@sanity/icons'
import { useClient, useToast } from 'sanity'

export function DataExportTool() {
  const [exporting, setExporting] = useState(false)
  const [exportType, setExportType] = useState('players')
  const [includeInactive, setIncludeInactive] = useState(false)
  const [exportFormat, setExportFormat] = useState('csv')
  const [results, setResults] = useState(null)
  const client = useClient({ apiVersion: '2023-01-01' })
  const toast = useToast()

  const exportOptions = [
    { value: 'players', label: 'Players' },
    { value: 'staff', label: 'Staff' },
    { value: 'events', label: 'Events/Fixtures' },
    { value: 'news', label: 'News Articles' },
    { value: 'sponsors', label: 'Sponsors' },
    { value: 'gallery', label: 'Gallery Images' },
    { value: 'all', label: 'All Data' }
  ]

  const formatOptions = [
    { value: 'csv', label: 'CSV' },
    { value: 'json', label: 'JSON' }
  ]

  const buildQuery = useCallback((type) => {
    const baseQueries = {
      players: `*[_type == "player"${!includeInactive ? ' && isActive == true' : ''}] | order(position asc, jerseyNumber asc) {
        name,
        position,
        role,
        jerseyNumber,
        bio,
        isActive,
        "appearances": stats.appearances,
        "goals": stats.goals,
        "assists": stats.assists,
        "cleanSheets": stats.cleanSheets,
        _createdAt,
        _updatedAt
      }`,
      staff: `*[_type == "staff"${!includeInactive ? ' && isActive == true' : ''}] | order(role asc, name asc) {
        name,
        role,
        bio,
        "email": contactInfo.email,
        "phone": contactInfo.phone,
        isActive,
        _createdAt,
        _updatedAt
      }`,
      events: `*[_type == "event"] | order(date desc) {
        title,
        date,
        location,
        eventType,
        opponent,
        homeOrAway,
        result,
        _createdAt,
        _updatedAt
      }`,
      news: `*[_type == "newsArticle"] | order(publishedAt desc) {
        title,
        summary,
        publishedAt,
        "author": author->name,
        _createdAt,
        _updatedAt
      }`,
      sponsors: `*[_type == "sponsor"] | order(name asc) {
        name,
        website,
        sponsorshipLevel,
        _createdAt,
        _updatedAt
      }`,
      gallery: `*[_type == "galleryImage"] | order(_createdAt desc) {
        title,
        description,
        category,
        _createdAt,
        _updatedAt
      }`
    }
    return baseQueries[type] || baseQueries.players
  }, [includeInactive])

  const convertToCSV = (data, type) => {
    if (!data || data.length === 0) return ''
    
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header]
          if (value === null || value === undefined) return ''
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')
    
    return csvContent
  }

  const downloadFile = (content, filename, mimeType) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleExport = async () => {
    setExporting(true)
    setResults(null)

    try {
      let data
      let filename
      
      if (exportType === 'all') {
        // Export all data types
        const [players, staff, events, news, sponsors, gallery] = await Promise.all([
          client.fetch(buildQuery('players')),
          client.fetch(buildQuery('staff')),
          client.fetch(buildQuery('events')),
          client.fetch(buildQuery('news')),
          client.fetch(buildQuery('sponsors')),
          client.fetch(buildQuery('gallery'))
        ])
        
        data = { players, staff, events, news, sponsors, gallery }
        filename = `nnufc-all-data-${new Date().toISOString().split('T')[0]}`
      } else {
        data = await client.fetch(buildQuery(exportType))
        filename = `nnufc-${exportType}-${new Date().toISOString().split('T')[0]}`
      }

      let content
      let mimeType
      
      if (exportFormat === 'csv') {
        if (exportType === 'all') {
          // For all data, create a zip-like structure or multiple files
          // For now, we'll export as JSON when all is selected
          content = JSON.stringify(data, null, 2)
          mimeType = 'application/json'
          filename += '.json'
        } else {
          content = convertToCSV(data, exportType)
          mimeType = 'text/csv'
          filename += '.csv'
        }
      } else {
        content = JSON.stringify(data, null, 2)
        mimeType = 'application/json'
        filename += '.json'
      }

      downloadFile(content, filename, mimeType)

      setResults({
        success: true,
        recordCount: Array.isArray(data) ? data.length : Object.values(data).reduce((sum, arr) => sum + (Array.isArray(arr) ? arr.length : 0), 0),
        filename,
        type: exportType
      })

      toast.push({
        status: 'success',
        title: 'Export successful',
        description: `${exportType} data exported successfully`
      })

    } catch (error) {
      toast.push({
        status: 'error',
        title: 'Export failed',
        description: error.message
      })
      setResults({
        success: false,
        error: error.message
      })
    } finally {
      setExporting(false)
    }
  }

  return (
    <Box padding={4}>
      <Card padding={4} radius={2} shadow={1}>
        <Stack space={4}>
          <Text size={3} weight="bold">Data Export Tool</Text>
          <Text size={2}>Export your club data in CSV or JSON format</Text>

          {!exporting && (
            <Grid columns={[1, 1, 2]} gap={4}>
              <Card padding={3} border radius={2}>
                <Stack space={4}>
                  <Text weight="semibold">Export Settings</Text>
                  
                  <Stack space={3}>
                    <Stack space={2}>
                      <Text size={1} weight="semibold">Data Type</Text>
                      <Select
                        value={exportType}
                        onChange={(event) => setExportType(event.currentTarget.value)}
                      >
                        {exportOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Select>
                    </Stack>

                    <Stack space={2}>
                      <Text size={1} weight="semibold">Format</Text>
                      <Select
                        value={exportFormat}
                        onChange={(event) => setExportFormat(event.currentTarget.value)}
                      >
                        {formatOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Select>
                    </Stack>

                    {(exportType === 'players' || exportType === 'staff' || exportType === 'all') && (
                      <Flex gap={2} align="center">
                        <Checkbox
                          checked={includeInactive}
                          onChange={(event) => setIncludeInactive(event.currentTarget.checked)}
                          id="include-inactive"
                        />
                        <Text size={1}>
                          <label htmlFor="include-inactive">Include inactive records</label>
                        </Text>
                      </Flex>
                    )}
                  </Stack>
                </Stack>
              </Card>

              <Card padding={3} border radius={2}>
                <Stack space={4}>
                  <Text weight="semibold">Export Preview</Text>
                  <Stack space={2}>
                    <Text size={1}>
                      <strong>Type:</strong> {exportOptions.find(opt => opt.value === exportType)?.label}
                    </Text>
                    <Text size={1}>
                      <strong>Format:</strong> {exportFormat.toUpperCase()}
                    </Text>
                    <Text size={1}>
                      <strong>Include Inactive:</strong> {includeInactive ? 'Yes' : 'No'}
                    </Text>
                  </Stack>
                  
                  <Button
                    text="Export Data"
                    icon={DownloadIcon}
                    tone="primary"
                    onClick={handleExport}
                    disabled={exporting}
                  />
                </Stack>
              </Card>
            </Grid>
          )}

          {exporting && (
            <Flex align="center" justify="center" direction="column" padding={5}>
              <Spinner muted />
              <Box marginTop={3}>
                <Text>Exporting {exportType} data...</Text>
              </Box>
            </Flex>
          )}

          {results && (
            <Card 
              padding={3} 
              border 
              radius={2} 
              tone={results.success ? "positive" : "critical"}
            >
              <Stack space={3}>
                <Flex justify="space-between" align="center">
                  <Text weight="semibold">Export Results</Text>
                  <Button 
                    text="Export Again" 
                    mode="ghost" 
                    onClick={() => setResults(null)} 
                  />
                </Flex>

                {results.success ? (
                  <Stack space={2}>
                    <Flex gap={2}>
                      <Badge tone="positive">Success</Badge>
                      <Text size={1}>{results.recordCount} records exported</Text>
                    </Flex>
                    <Text size={1}>
                      <strong>File:</strong> {results.filename}
                    </Text>
                  </Stack>
                ) : (
                  <Stack space={2}>
                    <Badge tone="critical">Error</Badge>
                    <Text size={1}>{results.error}</Text>
                  </Stack>
                )}
              </Stack>
            </Card>
          )}
        </Stack>
      </Card>
    </Box>
  )
}
