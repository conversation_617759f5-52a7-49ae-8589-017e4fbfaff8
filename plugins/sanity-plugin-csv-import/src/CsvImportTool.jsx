import React, {useState, useRef, useCallback, useEffect} from 'react'
import {<PERSON>, Card, <PERSON>ton, <PERSON>lex, <PERSON>ack, <PERSON>, Badge, Spinner, Grid, Checkbox} from '@sanity/ui'
import {UploadIcon, DownloadIcon, TrashIcon} from '@sanity/icons'
import {useToast} from '@sanity/ui'
import <PERSON> from 'papaparse'
import {useClient} from 'sanity'

// Sample templates as strings
const PLAYER_TEMPLATE = `name,position,role,jerseyNumber,bio,isActive,appearances,goals,assists,cleanSheets
<PERSON>,Forward,Captain,10,"<PERSON> is a talented forward with over 5 years of experience.",true,25,12,5,
<PERSON>,Midfielder,Vice Captain,8,"<PERSON> is known for her creative passes and midfield control.",true,30,5,15,
<PERSON>,Defender,Player,4,"<PERSON> is a solid defender who excels in aerial duels.",true,28,2,3,
<PERSON>,Goalkeeper,Player,1,"<PERSON> is a reliable goalkeeper with quick reflexes.",true,22,,,10`;

const STAFF_TEMPLATE = `name,role,bio,isActive,email,phone
<PERSON>,Head Coach,"<PERSON> has over 15 years of coaching experience and joined the club in 2018.",true,<EMAIL>,************
<PERSON>,Assistant Coach,"<PERSON> specializes in player development and offensive strategy.",true,<EMAIL>,
<PERSON>,Team Manager,"<PERSON> handles all administrative aspects of the team.",true,<EMAIL>,************
<PERSON> Brown,Physio,"<PERSON> is responsible for player injury prevention and rehabilitation.",true,<EMAIL>,************`;

// Helper function to generate a slug from a string
function generateSlug(input) {
  return input
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Remove consecutive hyphens
}

export function CsvImportTool() {
  const [importing, setImporting] = useState(false)
  const [importType, setImportType] = useState('')
  const [results, setResults] = useState(null)
  const [downloadPayload, setDownloadPayload] = useState(null)
  const [deleting, setDeleting] = useState(false)
  const [preventDuplicates, setPreventDuplicates] = useState(true)
  const client = useClient({apiVersion: '2023-01-01'})
  const toast = useToast()

  const playerInputRef = useRef(null)
  const staffInputRef = useRef(null)

  // Bulk delete all players
  const deleteAllPlayers = async () => {
    if (!window.confirm('Are you sure you want to delete ALL players? This action cannot be undone.')) {
      return
    }

    setDeleting(true)
    try {
      // Fetch all player IDs
      const playerIds = await client.fetch(`*[_type == "player"]._id`)

      if (playerIds.length === 0) {
        toast.push({
          status: 'info',
          title: 'No players found',
          description: 'There are no players to delete.'
        })
        return
      }

      // Delete all players in batches
      const batchSize = 100
      let deletedCount = 0

      for (let i = 0; i < playerIds.length; i += batchSize) {
        const batch = playerIds.slice(i, i + batchSize)
        await client.delete(batch)
        deletedCount += batch.length
      }

      toast.push({
        status: 'success',
        title: 'Players deleted',
        description: `Successfully deleted ${deletedCount} players.`
      })

      // Clear any existing results
      setResults(null)
    } catch (error) {
      toast.push({
        status: 'error',
        title: 'Delete failed',
        description: error.message
      })
    } finally {
      setDeleting(false)
    }
  }

  // Check for duplicate players
  const checkDuplicatePlayer = async (name, jerseyNumber) => {
    const query = `*[_type == "player" && (name == $name || jerseyNumber == $jerseyNumber)]`
    const params = { name, jerseyNumber: parseInt(jerseyNumber, 10) || 0 }
    const existing = await client.fetch(query, params)
    return existing
  }

  // Process player data import with duplicate prevention
  const importPlayers = async (data, preventDuplicates = true) => {
    const validPositions = ['Goalkeeper', 'Defender', 'Midfielder', 'Forward']
    const validRoles = ['Player', 'Captain', 'Vice Captain']
    let successCount = 0
    let errorCount = 0
    let skippedCount = 0
    const errors = []
    const skipped = []

    for (const player of data) {
      try {
        // Skip if name is missing
        if (!player.name) {
          errors.push(`Missing name for a player record`)
          errorCount++
          continue
        }

        // Skip if position is invalid
        if (!validPositions.includes(player.position)) {
          errors.push(`Invalid position "${player.position}" for ${player.name}, must be one of: ${validPositions.join(', ')}`)
          errorCount++
          continue
        }

        // Check for duplicates if prevention is enabled
        if (preventDuplicates) {
          const duplicates = await checkDuplicatePlayer(player.name, player.jerseyNumber)
          if (duplicates.length > 0) {
            skipped.push(`Player "${player.name}" already exists (duplicate name or jersey number)`)
            skippedCount++
            continue
          }
        }

        // Validate role if provided
        const role = player.role || 'Player'
        if (!validRoles.includes(role)) {
          errors.push(`Invalid role "${role}" for ${player.name}, must be one of: ${validRoles.join(', ')}`)
          errorCount++
          continue
        }

        // Prepare document for Sanity
        const doc = {
          _type: 'player',
          name: player.name,
          slug: {
            _type: 'slug',
            current: player.slug || generateSlug(player.name)
          },
          position: player.position,
          role: role,
          jerseyNumber: player.jerseyNumber ? parseInt(player.jerseyNumber, 10) : 0,
          bio: player.bio || '',
          isActive: player.isActive !== 'false', // Default to active if not specified
          stats: {
            appearances: player.appearances ? parseInt(player.appearances, 10) : 0,
            goals: player.position !== 'Goalkeeper' ? (player.goals ? parseInt(player.goals, 10) : 0) : undefined,
            assists: player.position !== 'Goalkeeper' ? (player.assists ? parseInt(player.assists, 10) : 0) : undefined,
            cleanSheets: player.position === 'Goalkeeper' ? (player.cleanSheets ? parseInt(player.cleanSheets, 10) : 0) : undefined
          }
        }

        // Create document in Sanity
        await client.create(doc)
        successCount++
      } catch (error) {
        errors.push(`Error importing ${player.name}: ${error.message}`)
        errorCount++
      }
    }

    return {
      successCount,
      errorCount,
      skippedCount,
      errors,
      skipped,
      total: data.length
    }
  }

  // Process staff data import
  const importStaff = async (data) => {
    const validRoles = ['Head Coach', 'Assistant Coach', 'Team Manager', 'Physio', 'Technical Director', 'Other']
    let successCount = 0
    let errorCount = 0
    const errors = []
    const warnings = []

    for (const staff of data) {
      try {
        // Skip if name is missing
        if (!staff.name) {
          errors.push(`Missing name for a staff record`)
          errorCount++
          continue
        }

        // Validate role
        if (staff.role && !validRoles.includes(staff.role)) {
          warnings.push(`Warning: Role "${staff.role}" for ${staff.name} is not one of the standard roles`)
        }

        // Prepare contact info object
        const contactInfo = {}
        if (staff.email) contactInfo.email = staff.email
        if (staff.phone) contactInfo.phone = staff.phone

        // Prepare document for Sanity
        const doc = {
          _type: 'staff',
          name: staff.name,
          slug: {
            _type: 'slug',
            current: staff.slug || generateSlug(staff.name)
          },
          role: staff.role || 'Other',
          bio: staff.bio || '',
          isActive: staff.isActive !== 'false', // Default to active if not specified
        }

        // Only add contactInfo if we have email or phone
        if (Object.keys(contactInfo).length > 0) {
          doc.contactInfo = contactInfo
        }

        // Create document in Sanity
        await client.create(doc)
        successCount++
      } catch (error) {
        errors.push(`Error importing ${staff.name}: ${error.message}`)
        errorCount++
      }
    }

    return {
      successCount,
      errorCount,
      errors,
      warnings,
      total: data.length
    }
  }

  const handleFileUpload = (event, type) => {
    const file = event.target.files[0]
    if (!file) return

    setImporting(true)
    setImportType(type)
    setResults(null)

    Papa.parse(file, {
      header: true,
      complete: async (results) => {
        try {
          let importResults

          if (type === 'players') {
            importResults = await importPlayers(results.data, preventDuplicates)
          } else if (type === 'staff') {
            importResults = await importStaff(results.data)
          }

          setResults(importResults)

          if (importResults.successCount > 0) {
            toast.push({
              status: 'success',
              title: `Import successful`,
              description: `${importResults.successCount} ${type} imported successfully.`
            })
          }

          if (importResults.skippedCount > 0) {
            toast.push({
              status: 'warning',
              title: `Duplicates skipped`,
              description: `${importResults.skippedCount} ${type} were skipped as duplicates.`
            })
          }

          if (importResults.errorCount > 0) {
            toast.push({
              status: 'error',
              title: `Import errors`,
              description: `${importResults.errorCount} errors occurred during import.`
            })
          }
        } catch (error) {
          toast.push({
            status: 'error',
            title: 'Import failed',
            description: error.message
          })
        } finally {
          setImporting(false)
        }
      },
      error: (error) => {
        toast.push({
          status: 'error',
          title: 'CSV parsing error',
          description: error.message
        })
        setImporting(false)
      }
    })
  }

  const resetForm = () => {
    setResults(null)
    setImportType('')
  }

  // Handle template downloads
  const handleDownloadTemplate = useCallback((type) => {
    if (type === 'players') {
      setDownloadPayload({
        content: PLAYER_TEMPLATE,
        filename: 'players-template.csv'
      });
      toast.push({
        status: 'info',
        title: 'Template Downloaded',
        description: 'Players CSV template downloaded successfully.'
      });
    } else if (type === 'staff') {
      setDownloadPayload({
        content: STAFF_TEMPLATE,
        filename: 'staff-template.csv'
      });
      toast.push({
        status: 'info',
        title: 'Template Downloaded',
        description: 'Staff CSV template downloaded successfully.'
      });
    }
  }, [toast]);

  // Handle actual download in useEffect
  useEffect(() => {
    if (!downloadPayload) return;

    try {
      // This runs only in the browser
      const blob = new Blob([downloadPayload.content], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', downloadPayload.filename);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Reset after download
      setDownloadPayload(null);
    } catch (err) {
      console.error('Download failed:', err);
    }
  }, [downloadPayload]);

  return (
    <Box padding={4}>
      <Card padding={4} radius={2} shadow={1}>
        <Stack space={4}>
          <Text size={3} weight="bold">CSV Import Tool</Text>
          <Text size={2}>Import player and staff data from CSV files</Text>

          {/* Settings and Controls */}
          <Card padding={3} border radius={2} tone="transparent">
            <Stack space={3}>
              <Text weight="semibold">Settings & Controls</Text>

              <Flex gap={3} wrap="wrap">
                <Checkbox
                  checked={preventDuplicates}
                  onChange={(event) => setPreventDuplicates(event.currentTarget.checked)}
                  id="prevent-duplicates"
                />
                <Box flex={1}>
                  <Text size={1}>
                    <label htmlFor="prevent-duplicates">
                      Prevent duplicate players (checks name and jersey number)
                    </label>
                  </Text>
                </Box>
              </Flex>

              <Flex gap={2}>
                <Button
                  text={deleting ? "Deleting..." : "Delete All Players"}
                  icon={TrashIcon}
                  tone="critical"
                  mode="ghost"
                  disabled={deleting || importing}
                  onClick={deleteAllPlayers}
                />
                <Text size={1} muted>
                  Use this to clear all existing players before importing new data
                </Text>
              </Flex>
            </Stack>
          </Card>

          {!importing && !results && (
            <Flex gap={3} direction={['column', 'column', 'row']}>
              <Card padding={3} border radius={2} tone="primary">
                <Stack space={3}>
                  <Text weight="semibold">Import Players</Text>
                  <Text size={1}>
                    Required columns: name, position (Goalkeeper, Defender, Midfielder, Forward). Optional: role (Player, Captain, Vice Captain)
                  </Text>
                  <Grid columns={2} gap={2}>
                    <Button
                      text="Select Players CSV"
                      icon={UploadIcon}
                      tone="primary"
                      onClick={() => playerInputRef.current?.click()}
                    />
                    <Button
                      text="Download Template"
                      icon={DownloadIcon}
                      mode="ghost"
                      onClick={() => handleDownloadTemplate('players')}
                    />
                  </Grid>
                  <input
                    ref={playerInputRef}
                    type="file"
                    accept=".csv"
                    style={{display: 'none'}}
                    onChange={(e) => handleFileUpload(e, 'players')}
                  />
                </Stack>
              </Card>

              <Card padding={3} border radius={2} tone="primary">
                <Stack space={3}>
                  <Text weight="semibold">Import Staff</Text>
                  <Text size={1}>
                    Required columns: name
                  </Text>
                  <Grid columns={2} gap={2}>
                    <Button
                      text="Select Staff CSV"
                      icon={UploadIcon}
                      tone="primary"
                      onClick={() => staffInputRef.current?.click()}
                    />
                    <Button
                      text="Download Template"
                      icon={DownloadIcon}
                      mode="ghost"
                      onClick={() => handleDownloadTemplate('staff')}
                    />
                  </Grid>
                  <input
                    ref={staffInputRef}
                    type="file"
                    accept=".csv"
                    style={{display: 'none'}}
                    onChange={(e) => handleFileUpload(e, 'staff')}
                  />
                </Stack>
              </Card>
            </Flex>
          )}

          {importing && (
            <Flex align="center" justify="center" direction="column" padding={5}>
              <Spinner muted />
              <Box marginTop={3}>
                <Text>Importing {importType}...</Text>
              </Box>
            </Flex>
          )}

          {results && (
            <Card padding={3} border radius={2} tone={results.errorCount > 0 ? "caution" : "positive"}>
              <Stack space={4}>
                <Flex justify="space-between" align="center">
                  <Text weight="semibold">Import Results</Text>
                  <Button text="Import Another File" mode="ghost" onClick={resetForm} />
                </Flex>

                <Flex gap={2}>
                  <Badge tone="positive">{results.successCount} Successful</Badge>
                  {results.errorCount > 0 && <Badge tone="critical">{results.errorCount} Errors</Badge>}
                  {results.skippedCount > 0 && <Badge tone="caution">{results.skippedCount} Skipped</Badge>}
                  <Text muted size={1}>Total: {results.total}</Text>
                </Flex>

                {results.errors && results.errors.length > 0 && (
                  <Card tone="critical" padding={3} radius={2}>
                    <Stack space={2}>
                      <Text size={1} weight="semibold">Errors:</Text>
                      {results.errors.slice(0, 5).map((error, i) => (
                        <Text size={1} key={i}>{error}</Text>
                      ))}
                      {results.errors.length > 5 && (
                        <Text size={1} muted>...and {results.errors.length - 5} more errors</Text>
                      )}
                    </Stack>
                  </Card>
                )}

                {results.skipped && results.skipped.length > 0 && (
                  <Card tone="caution" padding={3} radius={2}>
                    <Stack space={2}>
                      <Text size={1} weight="semibold">Skipped Duplicates:</Text>
                      {results.skipped.slice(0, 5).map((skipped, i) => (
                        <Text size={1} key={i}>{skipped}</Text>
                      ))}
                      {results.skipped.length > 5 && (
                        <Text size={1} muted>...and {results.skipped.length - 5} more skipped</Text>
                      )}
                    </Stack>
                  </Card>
                )}

                {results.warnings && results.warnings.length > 0 && (
                  <Card tone="caution" padding={3} radius={2}>
                    <Stack space={2}>
                      <Text size={1} weight="semibold">Warnings:</Text>
                      {results.warnings.slice(0, 5).map((warning, i) => (
                        <Text size={1} key={i}>{warning}</Text>
                      ))}
                      {results.warnings.length > 5 && (
                        <Text size={1} muted>...and {results.warnings.length - 5} more warnings</Text>
                      )}
                    </Stack>
                  </Card>
                )}
              </Stack>
            </Card>
          )}
        </Stack>
      </Card>
    </Box>
  )
}