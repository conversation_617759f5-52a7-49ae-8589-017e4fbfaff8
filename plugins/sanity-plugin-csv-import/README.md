# CSV Import Plugin for Sanity Studio

This custom Sanity Studio plugin adds CSV import functionality directly to the Sanity Studio interface, allowing content editors to easily import player and staff data from CSV files without needing to use command-line tools.

## Features

- Import players from CSV files with validation
- Import staff from CSV files with validation
- **Duplicate prevention** - Automatically detects and skips duplicate players based on name and jersey number
- **Bulk delete functionality** - Remove all players with confirmation dialog
- User-friendly interface with file selection buttons
- Detailed error and success reporting with duplicate tracking
- Progress indicators during import
- Settings panel for controlling duplicate prevention

## CSV Format Requirements

### Players CSV

Required columns:
- `name` (required) - Player's full name
- `position` (required) - Must be one of: Goalkeeper, Defender, Midfielder, Forward

Optional columns:
- `role` - Team role: Player, Captain, or Vice Captain (defaults to "Player" if not specified)
- `jerseyNumber` - Player's jersey number
- `bio` - Brief player biography
- `isActive` - Set to "false" for inactive players (defaults to true if not specified)
- `appearances` - Number of appearances
- `goals` - Number of goals (for non-goalkeepers)
- `cleanSheets` - Number of clean sheets (for goalkeepers)
- `assists` - Number of assists (for non-goalkeepers)

### Staff CSV

Required columns:
- `name` (required) - Staff member's full name

Optional columns:
- `role` - Staff role (e.g., Head Coach, Assistant Coach)
- `bio` - Brief staff biography
- `isActive` - Set to "false" for inactive staff (defaults to true if not specified)
- `email` - Contact email address
- `phone` - Contact phone number

## Usage

### Basic Import Process

1. Navigate to the "CSV Import" tool in the Sanity Studio sidebar
2. Configure settings in the "Settings & Controls" section:
   - **Prevent duplicate players**: Toggle to enable/disable duplicate detection
   - **Delete All Players**: Use this button to clear existing players before importing
3. Select the type of data you want to import (Players or Staff)
4. Click the corresponding button to select a CSV file
5. Wait for the import to complete
6. View the import results, including any errors, warnings, and skipped duplicates

### Duplicate Management

The plugin now includes robust duplicate prevention:

- **Automatic Detection**: Checks for existing players with the same name OR jersey number
- **Skip Duplicates**: When enabled, duplicate players are automatically skipped during import
- **Detailed Reporting**: Shows exactly which players were skipped and why
- **Bulk Delete**: Option to delete all existing players before importing new data

### Managing Existing Duplicates

If you already have duplicate players in your system:

1. Use the **Delete All Players** button to remove all existing players
2. Re-import your clean CSV file with duplicate prevention enabled
3. Alternatively, use the command-line script: `node scripts/manage-players.js remove-duplicates`

## Command Line Tools

For advanced management, use the included command-line script:

```bash
# List all players
SANITY_PROJECT_ID=your-id SANITY_TOKEN=your-token node scripts/manage-players.js list

# Remove duplicate players (keeps oldest of each duplicate group)
SANITY_PROJECT_ID=your-id SANITY_TOKEN=your-token node scripts/manage-players.js remove-duplicates

# Delete all players
SANITY_PROJECT_ID=your-id SANITY_TOKEN=your-token node scripts/manage-players.js delete-all
```