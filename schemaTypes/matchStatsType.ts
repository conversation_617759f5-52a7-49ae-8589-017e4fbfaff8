import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'matchStats',
  title: 'Match Statistics',
  type: 'document',
  fields: [
    defineField({
      name: 'match',
      title: 'Match',
      type: 'reference',
      to: [{type: 'event'}],
      validation: (Rule) => Rule.required(),
      options: {
        filter: 'eventType == "match"'
      }
    }),
    defineField({
      name: 'season',
      title: 'Season',
      type: 'reference',
      to: [{type: 'season'}],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'playerStats',
      title: 'Player Statistics',
      type: 'array',
      of: [{
        type: 'object',
        fields: [
          defineField({
            name: 'player',
            title: 'Player',
            type: 'reference',
            to: [{type: 'player'}],
            validation: (Rule) => Rule.required(),
          }),
          defineField({
            name: 'minutesPlayed',
            title: 'Minutes Played',
            type: 'number',
            validation: (Rule) => Rule.min(0).max(120),
            initialValue: 0
          }),
          defineField({
            name: 'goals',
            title: 'Goals',
            type: 'number',
            validation: (Rule) => Rule.min(0),
            initialValue: 0
          }),
          defineField({
            name: 'assists',
            title: 'Assists',
            type: 'number',
            validation: (Rule) => Rule.min(0),
            initialValue: 0
          }),
          defineField({
            name: 'yellowCards',
            title: 'Yellow Cards',
            type: 'number',
            validation: (Rule) => Rule.min(0).max(2),
            initialValue: 0
          }),
          defineField({
            name: 'redCards',
            title: 'Red Cards',
            type: 'number',
            validation: (Rule) => Rule.min(0).max(1),
            initialValue: 0
          }),
          defineField({
            name: 'saves',
            title: 'Saves (Goalkeeper)',
            type: 'number',
            validation: (Rule) => Rule.min(0),
            initialValue: 0,
            hidden: ({parent}) => {
              // Only show for goalkeepers - would need to check player position
              return false // For now, show for all players
            }
          }),
          defineField({
            name: 'rating',
            title: 'Player Rating',
            type: 'number',
            validation: (Rule) => Rule.min(1).max(10),
            description: 'Player performance rating (1-10)'
          }),
          defineField({
            name: 'wasSubstituted',
            title: 'Was Substituted',
            type: 'boolean',
            initialValue: false
          }),
          defineField({
            name: 'substitutionMinute',
            title: 'Substitution Minute',
            type: 'number',
            validation: (Rule) => Rule.min(0).max(120),
            hidden: ({parent}) => !parent?.wasSubstituted
          }),
        ],
        preview: {
          select: {
            playerName: 'player.name',
            goals: 'goals',
            assists: 'assists',
            minutesPlayed: 'minutesPlayed'
          },
          prepare({playerName, goals, assists, minutesPlayed}) {
            return {
              title: playerName || 'Unknown Player',
              subtitle: `${goals}G ${assists}A - ${minutesPlayed}min`
            }
          }
        }
      }]
    }),
    defineField({
      name: 'teamStats',
      title: 'Team Statistics',
      type: 'object',
      fields: [
        defineField({
          name: 'possession',
          title: 'Possession %',
          type: 'number',
          validation: (Rule) => Rule.min(0).max(100),
        }),
        defineField({
          name: 'shots',
          title: 'Total Shots',
          type: 'number',
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'shotsOnTarget',
          title: 'Shots on Target',
          type: 'number',
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'corners',
          title: 'Corner Kicks',
          type: 'number',
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'fouls',
          title: 'Fouls',
          type: 'number',
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'offsides',
          title: 'Offsides',
          type: 'number',
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'passAccuracy',
          title: 'Pass Accuracy %',
          type: 'number',
          validation: (Rule) => Rule.min(0).max(100),
        }),
      ]
    }),
    defineField({
      name: 'notes',
      title: 'Match Notes',
      type: 'text',
      rows: 4,
      description: 'Additional notes about the match performance'
    }),
  ],
  preview: {
    select: {
      matchTitle: 'match.title',
      seasonName: 'season.name',
      playerCount: 'playerStats'
    },
    prepare({matchTitle, seasonName, playerCount}) {
      const playerStatsCount = Array.isArray(playerCount) ? playerCount.length : 0
      return {
        title: matchTitle || 'Unknown Match',
        subtitle: `${seasonName || 'No Season'} - ${playerStatsCount} players`
      }
    },
  },
})
