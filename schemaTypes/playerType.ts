import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'player',
  title: 'Player',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Player Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {source: 'name', maxLength: 96},
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'position',
      title: 'Position',
      type: 'string',
      options: {
        list: [
          {title: 'Goalkeeper', value: 'Goalkeeper'},
          {title: 'Defender', value: 'Defender'},
          {title: 'Midfielder', value: 'Midfielder'},
          {title: 'Forward', value: 'Forward'},
        ],
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'role',
      title: 'Team Role',
      type: 'string',
      options: {
        list: [
          {title: 'Player', value: 'Player'},
          {title: 'Captain', value: 'Captain'},
          {title: 'Vice Captain', value: 'Vice Captain'},
        ],
      },
      initialValue: 'Player',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'jerseyNumber',
      title: 'Jersey Number',
      type: 'number',
      validation: (Rule) => Rule.required().integer().positive().max(99),
    }),
    defineField({
      name: 'image',
      title: 'Player Photo',
      type: 'image',
      options: {
        hotspot: true, // Enables more flexible cropping
      },
      fields: [
        defineField({
          name: 'alt',
          title: 'Alt Text',
          type: 'string',
          // Alt text is only required if image is provided
          validation: (Rule) => Rule.custom((alt, context) => {
            const parent = context.parent as any;
            if (parent && !alt) {
              return 'Alt text is required when image is provided';
            }
            return true;
          }),
        }),
      ],
      // Image is now optional - no validation rule
    }),
    defineField({
      name: 'stats',
      title: 'Statistics',
      type: 'object',
      fields: [
        defineField({
          name: 'appearances',
          title: 'Appearances',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'goals',
          title: 'Goals',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
          hidden: ({document}) => document?.position === 'Goalkeeper',
        }),
        defineField({
          name: 'assists',
          title: 'Assists',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
          hidden: ({document}) => document?.position === 'Goalkeeper',
        }),
        defineField({
          name: 'cleanSheets',
          title: 'Clean Sheets',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
          hidden: ({document}) => document?.position !== 'Goalkeeper',
        }),
      ],
    }),
    defineField({
      name: 'bio',
      title: 'Player Bio',
      type: 'text',
      rows: 4,
    }),
    defineField({
      name: 'isActive',
      title: 'Active Player',
      type: 'boolean',
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'position',
      media: 'image',
      number: 'jerseyNumber',
    },
    prepare({title, subtitle, media, number}) {
      return {
        title: `${number ? '#' + number : ''} ${title}`,
        subtitle: subtitle || 'Player',
        media,
      }
    },
  },
})