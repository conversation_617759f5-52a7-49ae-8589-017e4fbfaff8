import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'player',
  title: 'Player',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Player Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {source: 'name', maxLength: 96},
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'position',
      title: 'Main Position Category',
      type: 'string',
      options: {
        list: [
          {title: 'Goalkeeper', value: 'Goalkeeper'},
          {title: 'Defender', value: 'Defender'},
          {title: 'Midfielder', value: 'Midfielder'},
          {title: 'Forward', value: 'Forward'},
        ],
      },
      validation: (Rule) => Rule.required(),
      description: 'Main position category for team organization',
    }),
    defineField({
      name: 'specificPosition',
      title: 'Specific Position',
      type: 'string',
      options: {
        list: [
          // Goalkeeper positions
          {title: 'Goalkeeper', value: 'Goalkeeper'},

          // Defender positions
          {title: 'Centre-Back (CB)', value: 'Centre-Back'},
          {title: 'Left-Back (LB)', value: 'Left-Back'},
          {title: 'Right-Back (RB)', value: 'Right-Back'},
          {title: 'Wing-Back (WB)', value: 'Wing-Back'},
          {title: 'Sweeper', value: 'Sweeper'},

          // Midfielder positions
          {title: 'Defensive Midfielder (CDM)', value: 'Defensive Midfielder'},
          {title: 'Central Midfielder (CM)', value: 'Central Midfielder'},
          {title: 'Attacking Midfielder (CAM)', value: 'Attacking Midfielder'},
          {title: 'Left Midfielder (LM)', value: 'Left Midfielder'},
          {title: 'Right Midfielder (RM)', value: 'Right Midfielder'},
          {title: 'Left Winger (LW)', value: 'Left Winger'},
          {title: 'Right Winger (RW)', value: 'Right Winger'},
          {title: 'Box-to-Box Midfielder', value: 'Box-to-Box Midfielder'},

          // Forward positions
          {title: 'Striker (ST)', value: 'Striker'},
          {title: 'Centre-Forward (CF)', value: 'Centre-Forward'},
          {title: 'Left Forward (LF)', value: 'Left Forward'},
          {title: 'Right Forward (RF)', value: 'Right Forward'},
          {title: 'Second Striker (SS)', value: 'Second Striker'},
          {title: 'False 9', value: 'False 9'},
        ],
      },
      validation: (Rule) => Rule.required(),
      description: 'Specific playing position and role on the field',
    }),
    defineField({
      name: 'role',
      title: 'Team Role',
      type: 'string',
      options: {
        list: [
          {title: 'Player', value: 'Player'},
          {title: 'Captain', value: 'Captain'},
          {title: 'Vice Captain', value: 'Vice Captain'},
        ],
      },
      initialValue: 'Player',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'jerseyNumber',
      title: 'Jersey Number',
      type: 'number',
      validation: (Rule) => Rule.required().integer().positive().max(99),
    }),
    defineField({
      name: 'image',
      title: 'Player Photo',
      type: 'image',
      options: {
        hotspot: true, // Enables more flexible cropping
      },
      fields: [
        defineField({
          name: 'alt',
          title: 'Alt Text',
          type: 'string',
          // Alt text is only required if image is provided
          validation: (Rule) => Rule.custom((alt, context) => {
            const parent = context.parent as any;
            if (parent && !alt) {
              return 'Alt text is required when image is provided';
            }
            return true;
          }),
        }),
      ],
      // Image is now optional - no validation rule
    }),
    defineField({
      name: 'stats',
      title: 'Statistics',
      type: 'object',
      fields: [
        defineField({
          name: 'appearances',
          title: 'Appearances',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
        }),
        defineField({
          name: 'goals',
          title: 'Goals',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
          hidden: ({document}) => document?.position === 'Goalkeeper',
        }),
        defineField({
          name: 'assists',
          title: 'Assists',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
          hidden: ({document}) => document?.position === 'Goalkeeper',
        }),
        defineField({
          name: 'cleanSheets',
          title: 'Clean Sheets',
          type: 'number',
          initialValue: 0,
          validation: (Rule) => Rule.min(0),
          hidden: ({document}) => document?.position !== 'Goalkeeper',
        }),
      ],
    }),
    defineField({
      name: 'bio',
      title: 'Player Bio',
      type: 'text',
      rows: 4,
    }),
    defineField({
      name: 'isActive',
      title: 'Active Player',
      type: 'boolean',
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      title: 'name',
      position: 'position',
      specificPosition: 'specificPosition',
      media: 'image',
      number: 'jerseyNumber',
      role: 'role',
    },
    prepare({title, position, specificPosition, media, number, role}) {
      const displayPosition = specificPosition || position || 'Player'
      const rolePrefix = role === 'Captain' ? '(C) ' : role === 'Vice Captain' ? '(VC) ' : ''

      return {
        title: `${rolePrefix}${number ? '#' + number : ''} ${title}`,
        subtitle: displayPosition,
        media,
      }
    },
  },
})