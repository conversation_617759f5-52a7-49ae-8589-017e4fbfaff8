import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'staff',
  title: 'Staff',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Staff Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {source: 'name', maxLength: 96},
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'role',
      title: 'Role',
      type: 'string',
      validation: (Rule) => Rule.required(),
      options: {
        list: [
          {title: 'Head Coach', value: 'Head Coach'},
          {title: 'Assistant Coach', value: 'Assistant Coach'},
          {title: 'Team Manager', value: 'Team Manager'},
          {title: 'Phys<PERSON>', value: 'Physio'},
          {title: 'Technical Director', value: 'Technical Director'},
          {title: 'Other', value: 'Other'},
        ],
      },
    }),
    defineField({
      name: 'image',
      title: 'Staff Photo',
      type: 'image',
      options: {
        hotspot: true, // Enables more flexible cropping
      },
      fields: [
        defineField({
          name: 'alt',
          title: 'Alt Text',
          type: 'string',
          // Alt text is only required if image is provided
          validation: (Rule) => Rule.custom((alt, context) => {
            const parent = context.parent as any;
            if (parent && !alt) {
              return 'Alt text is required when image is provided';
            }
            return true;
          }),
        }),
      ],
      // Image is now optional - no validation rule
    }),
    defineField({
      name: 'bio',
      title: 'Biography',
      type: 'text',
      rows: 4,
    }),
    defineField({
      name: 'contactInfo',
      title: 'Contact Information',
      type: 'object',
      fields: [
        defineField({
          name: 'email',
          title: 'Email',
          type: 'string',
          validation: (Rule) =>
            Rule.regex(
              /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
              {
                name: 'email',
                invert: false,
              }
            ).warning('Please enter a valid email address'),
        }),
        defineField({
          name: 'phone',
          title: 'Phone',
          type: 'string',
        }),
      ],
    }),
    defineField({
      name: 'isActive',
      title: 'Active Staff',
      type: 'boolean',
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'role',
      media: 'image',
    },
    prepare({title, subtitle, media}) {
      return {
        title,
        subtitle: subtitle || 'Staff Member',
        media,
      }
    },
  },
})