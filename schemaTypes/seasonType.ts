import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'season',
  title: 'Season',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Season Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
      placeholder: 'e.g., 2024 Season, Summer 2024'
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {source: 'name', maxLength: 96},
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'startDate',
      title: 'Season Start Date',
      type: 'date',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'endDate',
      title: 'Season End Date',
      type: 'date',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'isActive',
      title: 'Active Season',
      type: 'boolean',
      initialValue: false,
      description: 'Only one season should be active at a time'
    }),
    defineField({
      name: 'description',
      title: 'Season Description',
      type: 'text',
      rows: 3,
    }),
  ],
  preview: {
    select: {
      title: 'name',
      startDate: 'startDate',
      endDate: 'endDate',
      isActive: 'isActive'
    },
    prepare({title, startDate, endDate, isActive}) {
      const dateRange = startDate && endDate 
        ? `${new Date(startDate).getFullYear()} - ${new Date(endDate).getFullYear()}`
        : 'No dates set'
      
      return {
        title: `${title}${isActive ? ' (Active)' : ''}`,
        subtitle: dateRange,
      }
    },
  },
})
