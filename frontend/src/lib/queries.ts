// Query to fetch events/fixtures
export const eventsQuery = `
  *[_type == "event"] | order(date asc) {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result,
    description,
    eventImage
  }
`;

// Query to fetch news articles
export const newsArticlesQuery = `
  *[_type == "newsArticle"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary,
    body
  }
`;

// Query to fetch sponsors
export const sponsorsQuery = `
  *[_type == "sponsor"] | order(sponsorshipLevel asc, displayOrder asc) {
    _id,
    name,
    logo,
    websiteUrl,
    sponsorshipLevel,
    displayOrder,
    description
  }
`;

// Query to fetch gallery images
export const galleryImagesQuery = `
  *[_type == "galleryImage"] | order(dateTaken desc) {
    _id,
    title,
    imageFile,
    dateTaken
  }
`;

// Query to fetch active players ordered by role (captain first), then position and jersey number
export const playersQuery = `
  *[_type == "player" && isActive == true] | order(role desc, position asc, jerseyNumber asc) {
    _id,
    name,
    slug,
    position,
    specificPosition,
    role,
    jerseyNumber,
    image,
    stats,
    bio
  }
`;

// Query to fetch coaching staff
export const staffQuery = `
  *[_type == "staff" && isActive == true] | order(role asc) {
    _id,
    name,
    slug,
    role,
    image,
    bio,
    contactInfo
  }
`;