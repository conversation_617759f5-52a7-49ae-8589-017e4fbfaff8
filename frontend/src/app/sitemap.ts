import { MetadataRoute } from 'next';
import { client } from '@/lib/sanity';
import { allEventsQuery, allNewsArticlesQuery } from '@/lib/queries';

interface SanityEvent {
  slug: { current: string };
  date: string; // Assuming date is in ISO format
  _updatedAt?: string; // Sanity's automatic timestamp
}

interface SanityNewsArticle {
  slug: { current: string };
  publishedAt: string; // Assuming publishedAt is in ISO format
  _updatedAt?: string; // San<PERSON>'s automatic timestamp
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://nnufc.com';

  // Static pages with priority and change frequency
  const staticPages = [
    { route: '', priority: 1.0, changeFreq: 'weekly' as const }, // Homepage
    { route: '/about', priority: 0.8, changeFreq: 'monthly' as const },
    { route: '/team', priority: 0.9, changeFreq: 'weekly' as const },
    { route: '/news', priority: 0.9, changeFreq: 'daily' as const },
    { route: '/events', priority: 0.9, changeFreq: 'daily' as const },
    { route: '/sponsors', priority: 0.7, changeFreq: 'monthly' as const },
    { route: '/gallery', priority: 0.6, changeFreq: 'weekly' as const },
    { route: '/contact', priority: 0.8, changeFreq: 'monthly' as const },
  ].map((page) => ({
    url: `${baseUrl}${page.route}`,
    lastModified: new Date().toISOString(),
    changeFrequency: page.changeFreq,
    priority: page.priority,
  }));

  // Dynamic pages: Events
  const events: SanityEvent[] = await client.fetch(allEventsQuery);
  const eventUrls = events.map((event) => ({
    url: `${baseUrl}/events/${event.slug.current}`,
    lastModified: event._updatedAt || event.date || new Date().toISOString(),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }));

  // Dynamic pages: News Articles
  const newsArticles: SanityNewsArticle[] = await client.fetch(allNewsArticlesQuery);
  const newsUrls = newsArticles.map((article) => ({
    url: `${baseUrl}/news/${article.slug.current}`,
    lastModified: article._updatedAt || article.publishedAt || new Date().toISOString(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }));

  return [...staticPages, ...eventUrls, ...newsUrls];
}