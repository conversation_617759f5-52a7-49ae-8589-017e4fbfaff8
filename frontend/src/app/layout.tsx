import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import StructuredData from "../../components/StructuredData";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const siteName = "Northern Nepalese United FC";
const defaultTitle = "Northern Nepalese United FC - NNUFC | Brisbane Football Club";
const defaultDescription = "Official website of Northern Nepalese United Football Club (NNUFC) - Brisbane's premier Nepalese football team. Join our community, follow matches, news, events, and connect with fellow football enthusiasts in Queensland, Australia.";
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://nnufc.com';
const defaultOgImage = `${baseUrl}/logo.png`;
export const metadata: Metadata = {
  metadataBase: new URL(baseUrl),
  title: {
    default: defaultTitle,
    template: `%s | ${siteName}`,
  },
  description: defaultDescription,
  keywords: [
    'Northern Nepalese United FC',
    'NNUFC',
    'Brisbane football club',
    'Nepalese football team',
    'Queensland football',
    'Australian football',
    'soccer Brisbane',
    'Nepalese community Brisbane',
    'football club Australia',
    'soccer team Queensland',
    'Brisbane sports club',
    'multicultural football',
    'community football Brisbane'
  ],
  authors: [{ name: 'Northern Nepalese United FC' }],
  creator: 'Northern Nepalese United FC',
  publisher: 'Northern Nepalese United FC',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: defaultTitle,
    description: defaultDescription,
    url: baseUrl,
    siteName: siteName,
    images: [
      {
        url: defaultOgImage,
        width: 1200,
        height: 630,
        alt: 'Northern Nepalese United FC Logo',
      },
    ],
    locale: 'en_AU',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: defaultTitle,
    description: defaultDescription,
    images: [defaultOgImage],
    // creator: '@nnufc_official', // Add when Twitter account is created
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#1f2937' },
    ],
  },
  manifest: '/site.webmanifest',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#1f2937' },
  ],
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
  },
  verification: {
    // google: 'your-google-verification-code', // Add when Google Search Console is set up
    // bing: 'your-bing-verification-code', // Add when Bing Webmaster Tools is set up
  },
  category: 'sports',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en-AU" dir="ltr">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes" />
        <meta name="format-detection" content="telephone=no, date=no, email=no, address=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="NNUFC" />
        <meta name="application-name" content="NNUFC" />
        <meta name="msapplication-TileColor" content="#1f2937" />
        <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)" />
        <meta name="theme-color" content="#1f2937" media="(prefers-color-scheme: dark)" />
        <link rel="canonical" href={baseUrl} />
        <link rel="dns-prefetch" href="//cdn.sanity.io" />
        <link rel="preconnect" href="https://cdn.sanity.io" crossOrigin="anonymous" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-white text-gray-900 selection:bg-primary-100 selection:text-primary-900`}
        suppressHydrationWarning={true}
      >
        <div id="root" className="min-h-screen flex flex-col">
          {children}
        </div>
        <StructuredData type="organization" />
        <StructuredData type="website" />
        <noscript>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            background: '#f59e0b',
            color: 'white',
            padding: '1rem',
            textAlign: 'center',
            zIndex: 9999
          }}>
            This website requires JavaScript to function properly. Please enable JavaScript in your browser.
          </div>
        </noscript>
      </body>
    </html>
  );
}
