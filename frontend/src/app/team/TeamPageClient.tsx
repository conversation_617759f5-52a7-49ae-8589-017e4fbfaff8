'use client'

import { useEffect } from 'react'
import { useNNUFCAnalytics } from '../../hooks/useNNUFCAnalytics'

interface Player {
  _id: string;
  name: string;
  position: string;
  role: string;
  jerseyNumber: number;
  stats: {
    appearances: number;
    goals?: number;
    assists?: number;
    cleanSheets?: number;
  };
  bio?: string;
}

interface Staff {
  _id: string;
  name: string;
  role: string;
  bio?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
  };
}

interface TeamPageClientProps {
  players: Player[]
  staff: Staff[]
}

export default function TeamPageClient({ players, staff }: TeamPageClientProps) {
  const analytics = useNNUFCAnalytics()

  useEffect(() => {
    // Track page view
    analytics.trackNNUFCPageView('team', {
      total_players: players.length,
      total_staff: staff.length,
      positions: [...new Set(players.map(p => p.position))],
      has_captain: players.some(p => p.role === 'Captain')
    })

    // Track performance
    const startTime = performance.now()
    const handleLoad = () => {
      const loadTime = performance.now() - startTime
      analytics.trackPerformance('team', loadTime)
    }

    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
      return () => window.removeEventListener('load', handleLoad)
    }
  }, [analytics, players.length, staff.length, players])

  // Track scroll depth
  useEffect(() => {
    let maxScrollDepth = 0
    
    const handleScroll = () => {
      const scrollTop = window.pageYOffset
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercent = Math.round((scrollTop / docHeight) * 100)
      
      if (scrollPercent > maxScrollDepth) {
        maxScrollDepth = scrollPercent
        
        // Track milestone scroll depths
        if (scrollPercent >= 25 && maxScrollDepth < 25) {
          analytics.trackScrollDepth('team', 25)
        } else if (scrollPercent >= 50 && maxScrollDepth < 50) {
          analytics.trackScrollDepth('team', 50)
        } else if (scrollPercent >= 75 && maxScrollDepth < 75) {
          analytics.trackScrollDepth('team', 75)
        } else if (scrollPercent >= 90 && maxScrollDepth < 90) {
          analytics.trackScrollDepth('team', 90)
        }
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [analytics])

  // Track time on page
  useEffect(() => {
    const startTime = Date.now()
    
    const handleBeforeUnload = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000)
      analytics.trackTimeOnPage('team', timeSpent)
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [analytics])

  const handlePlayerClick = (player: Player) => {
    analytics.trackPlayerView(player.name, player.position)
  }

  const handleStaffClick = (member: Staff) => {
    analytics.trackStaffView(member.name, member.role)
  }

  const handleContactClick = (contactType: 'email' | 'phone', staffName: string) => {
    analytics.trackContactInfoClick(contactType)
    analytics.trackEvent('staff_contact_click', {
      staff_name: staffName,
      contact_type: contactType
    })
  }

  return {
    handlePlayerClick,
    handleStaffClick,
    handleContactClick
  }
}
