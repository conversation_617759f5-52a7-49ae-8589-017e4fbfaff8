import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Image from "next/image";
import { client } from "@/lib/sanity";
import { urlForImage } from "@/lib/sanity";
import { playersQuery, staffQuery } from "@/lib/queries";
import TeamPageClient from "./TeamPageClient";

export const metadata = {
  title: "Our Team | Northern Nepalese United FC",
  description: "Meet the players and coaching staff of Northern Nepalese United FC.",
};

// This makes the page dynamic and will revalidate every 60 seconds
export const revalidate = 60;

// Define types for player and staff
interface PlayerStats {
  appearances: number;
  goals?: number;
  assists?: number;
  cleanSheets?: number;
}

interface SanityImageReference {
  _type: string;
  asset: {
    _ref: string;
    _type: string;
  };
}

interface Player {
  _id: string;
  name: string;
  position: string;
  role: string;
  jerseyNumber: number;
  image?: SanityImageReference;
  stats: PlayerStats;
  bio?: string;
}

interface Staff {
  _id: string;
  name: string;
  role: string;
  image?: SanityImageReference;
  bio?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
  };
}

// Functions to fetch data from Sanity
async function getPlayers() {
  try {
    return await client.fetch(playersQuery);
  } catch (error) {
    console.error("Failed to fetch players:", error);
    return [];
  }
}

async function getStaff() {
  try {
    return await client.fetch(staffQuery);
  } catch (error) {
    console.error("Failed to fetch staff:", error);
    return [];
  }
}

export default async function TeamPage() {
  // Fetch data from Sanity - this will work once you have data in Sanity
  // For now, we'll use placeholder data as fallback
  let players: Player[] = await getPlayers();
  let staff: Staff[] = await getStaff();

  // Use fallback data if no data is returned from Sanity
  if (!players || players.length === 0) {
    players = [
      {
        _id: "1",
        name: "Ram Gurung",
        position: "Forward",
        role: "Captain",
        jerseyNumber: 9,
        stats: {
          appearances: 15,
          goals: 8,
          assists: 3,
        },
      },
      {
        _id: "2",
        name: "Suman Tamang",
        position: "Midfielder",
        role: "Player",
        jerseyNumber: 10,
        stats: {
          appearances: 18,
          goals: 4,
          assists: 9,
        },
      },
      {
        _id: "3",
        name: "Anish Rai",
        position: "Defender",
        role: "Player",
        jerseyNumber: 4,
        stats: {
          appearances: 20,
          goals: 1,
          assists: 2,
        },
      },
      {
        _id: "4",
        name: "Bipin Thapa",
        position: "Goalkeeper",
        role: "Player",
        jerseyNumber: 1,
        stats: {
          appearances: 17,
          cleanSheets: 5,
        },
      },
      {
        _id: "5",
        name: "Nabin Shrestha",
        position: "Midfielder",
        role: "Player",
        jerseyNumber: 8,
        stats: {
          appearances: 16,
          goals: 3,
          assists: 5,
        },
      },
      {
        _id: "6",
        name: "Sanjeev Magar",
        position: "Defender",
        role: "Player",
        jerseyNumber: 5,
        stats: {
          appearances: 14,
          goals: 0,
          assists: 1,
        },
      },
    ];
  }

  if (!staff || staff.length === 0) {
    staff = [
      {
        _id: "1",
        name: "Krishna Pun",
        role: "Head Coach",
        bio: "Former professional player with 10+ years of coaching experience.",
      },
      {
        _id: "2",
        name: "Binod Khatri",
        role: "Assistant Coach",
        bio: "Specializes in tactical development and player conditioning.",
      },
      {
        _id: "3",
        name: "Laxmi Thapa",
        role: "Team Manager",
        bio: "Handles team logistics, scheduling, and administration.",
      },
    ];
  }

  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-primary-800 text-white py-16">
          <div className="container">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Team</h1>
            <p className="text-xl max-w-3xl">
              Meet the players and coaching staff who represent Northern Nepalese United FC.
            </p>
          </div>
        </section>

        {/* Players Section */}
        <section className="py-16">
          <div className="container">
            <h2 className="text-3xl font-bold mb-10 text-center">Players</h2>

            {/* Captain Section */}
            {(() => {
              const captain = players.find(player => player.role === "Captain");
              if (captain) {
                return (
                  <div className="mb-12">
                    <h3 className="text-2xl font-bold mb-6 text-center text-primary-600">Team Captain</h3>
                    <div className="max-w-md mx-auto">
                      <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg shadow-lg overflow-hidden border-2 border-primary-200">
                        <div className="h-48 bg-neutral-100 relative">
                          {captain.image ? (
                            <Image
                              src={urlForImage(captain.image).url()}
                              alt={captain.name}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <Image
                              src="/placeholder-player.svg"
                              alt={captain.name}
                              fill
                              className="object-cover"
                            />
                          )}
                          <div className="absolute top-0 right-0 bg-yellow-500 text-white p-3 font-bold text-xl">
                            #{captain.jerseyNumber}
                          </div>
                          <div className="absolute top-0 left-0 bg-primary-600 text-white px-3 py-1 text-sm font-semibold">
                            CAPTAIN
                          </div>
                        </div>

                        <div className="p-6">
                          <h4 className="text-2xl font-bold mb-2 text-center">{captain.name}</h4>
                          <p className="text-primary-600 font-medium mb-4 text-center text-lg">{captain.position}</p>

                          <div className="grid grid-cols-3 gap-4 border-t border-primary-200 pt-4 text-center">
                            <div>
                              <p className="text-xs text-neutral-600 font-medium">Appearances</p>
                              <p className="font-bold text-xl text-primary-700">{captain.stats?.appearances || 0}</p>
                            </div>
                            <div>
                              <p className="text-xs text-neutral-600 font-medium">Goals</p>
                              <p className="font-bold text-xl text-primary-700">
                                {captain.position === "Goalkeeper" ? "-" : captain.stats?.goals || 0}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-neutral-600 font-medium">
                                {captain.position === "Goalkeeper" ? "Clean Sheets" : "Assists"}
                              </p>
                              <p className="font-bold text-xl text-primary-700">
                                {captain.position === "Goalkeeper"
                                  ? captain.stats?.cleanSheets || 0
                                  : captain.stats?.assists || 0
                                }
                              </p>
                            </div>
                          </div>

                          {captain.bio && (
                            <div className="mt-4 pt-4 border-t border-primary-200">
                              <p className="text-sm text-neutral-700 text-center">{captain.bio}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}

            {/* Other Players Section - Enhanced Design */}
            {(() => {
              const otherPlayers = players.filter(player => player.role !== "Captain");
              if (otherPlayers.length > 0) {
                // Group players by position for better organization
                const playersByPosition = otherPlayers.reduce((acc, player) => {
                  const position = player.position;
                  if (!acc[position]) {
                    acc[position] = [];
                  }
                  acc[position].push(player);
                  return acc;
                }, {} as Record<string, Player[]>);

                const positionOrder = ["Goalkeeper", "Defender", "Midfielder", "Forward"];
                const getPositionColor = (position: string) => {
                  switch (position) {
                    case "Goalkeeper": return "bg-green-500";
                    case "Defender": return "bg-blue-500";
                    case "Midfielder": return "bg-purple-500";
                    case "Forward": return "bg-red-500";
                    default: return "bg-gray-500";
                  }
                };

                return (
                  <div>
                    <h3 className="text-3xl font-bold mb-8 text-center">Squad</h3>

                    {positionOrder.map((position) => {
                      const positionPlayers = playersByPosition[position];
                      if (!positionPlayers || positionPlayers.length === 0) return null;

                      return (
                        <div key={position} className="mb-12">
                          <div className="flex items-center justify-center mb-6">
                            <div className={`${getPositionColor(position)} w-4 h-4 rounded-full mr-3`}></div>
                            <h4 className="text-2xl font-bold text-gray-800">{position}s</h4>
                            <div className={`${getPositionColor(position)} w-4 h-4 rounded-full ml-3`}></div>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                            {positionPlayers.map((player) => (
                              <div key={player._id} className="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 overflow-hidden border-2 border-gray-50 hover:border-primary-200">
                                {/* Decorative Background Pattern */}
                                <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-primary-100/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                {/* Player Image Section */}
                                <div className="relative h-40 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
                                  {player.image ? (
                                    <Image
                                      src={urlForImage(player.image).url()}
                                      alt={player.name}
                                      fill
                                      className="object-cover group-hover:scale-110 transition-transform duration-500 filter group-hover:brightness-110"
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 via-primary-50 to-primary-200 relative">
                                      {/* Animated Background Circles */}
                                      <div className="absolute inset-0 overflow-hidden">
                                        <div className="absolute -top-4 -left-4 w-16 h-16 bg-primary-300/20 rounded-full group-hover:scale-150 transition-transform duration-700"></div>
                                        <div className="absolute -bottom-4 -right-4 w-20 h-20 bg-primary-400/15 rounded-full group-hover:scale-125 transition-transform duration-700 delay-100"></div>
                                      </div>
                                      <div className="relative z-10 p-4 text-center">
                                        <div className="w-20 h-20 mx-auto mb-2 bg-primary-200 rounded-full flex items-center justify-center group-hover:bg-primary-300 transition-colors duration-300">
                                          <svg className="w-10 h-10 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                          </svg>
                                        </div>
                                        <p className="text-xs text-primary-600 font-medium">Photo Coming Soon</p>
                                      </div>
                                    </div>
                                  )}

                                  {/* Jersey Number Badge - Enhanced */}
                                  <div className={`absolute top-4 left-4 ${getPositionColor(player.position)} text-white text-lg font-black px-4 py-2 rounded-2xl shadow-2xl border-2 border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300`}>
                                    <span className="text-xs font-medium opacity-80">NO.</span>
                                    <br />
                                    <span className="text-xl leading-none">{player.jerseyNumber}</span>
                                  </div>

                                  {/* Position Badge - Enhanced */}
                                  <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-md text-gray-800 text-sm font-bold px-3 py-2 rounded-xl shadow-lg border border-gray-200/50 group-hover:bg-white group-hover:scale-105 transition-all duration-300">
                                    {player.position}
                                  </div>

                                  {/* Vice Captain Badge - Enhanced */}
                                  {player.role === "Vice Captain" && (
                                    <div className="absolute bottom-4 left-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-2 text-xs font-bold rounded-xl shadow-lg border-2 border-orange-300/50 group-hover:from-orange-400 group-hover:to-orange-500 transition-all duration-300">
                                      <div className="flex items-center space-x-1">
                                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        <span>VICE CAPTAIN</span>
                                      </div>
                                    </div>
                                  )}

                                  {/* Hover Overlay */}
                                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                </div>

                                {/* Player Info Section */}
                                <div className="relative p-4">
                                  {/* Name with decorative underline */}
                                  <div className="text-center mb-3">
                                    <h4 className="text-lg font-bold text-gray-800 group-hover:text-primary-600 transition-colors duration-300 mb-1">
                                      {player.name}
                                    </h4>
                                    <div className="w-10 h-1 bg-gradient-to-r from-primary-400 to-primary-600 rounded-full mx-auto group-hover:w-14 transition-all duration-300"></div>
                                  </div>

                                  {/* Enhanced Stats Grid */}
                                  <div className="grid grid-cols-3 gap-2 mt-3">
                                    <div className="text-center bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-3 group-hover:from-primary-50 group-hover:to-primary-100 transition-all duration-300 border border-gray-100 group-hover:border-primary-200 group-hover:shadow-md">
                                      <div className="w-6 h-6 mx-auto mb-1 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-300">
                                        <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                      </div>
                                      <p className="text-xs text-gray-500 font-semibold mb-1 uppercase tracking-wide">Apps</p>
                                      <p className="font-black text-lg text-gray-800 group-hover:text-primary-700 transition-colors">{player.stats?.appearances || 0}</p>
                                    </div>

                                    <div className="text-center bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-3 group-hover:from-primary-50 group-hover:to-primary-100 transition-all duration-300 border border-gray-100 group-hover:border-primary-200 group-hover:shadow-md">
                                      <div className="w-6 h-6 mx-auto mb-1 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                                        {player.position === "Goalkeeper" ? (
                                          <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                          </svg>
                                        ) : (
                                          <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                          </svg>
                                        )}
                                      </div>
                                      <p className="text-xs text-gray-500 font-semibold mb-1 uppercase tracking-wide">
                                        {player.position === "Goalkeeper" ? "Saves" : "Goals"}
                                      </p>
                                      <p className="font-black text-lg text-gray-800 group-hover:text-primary-700 transition-colors">
                                        {player.position === "Goalkeeper" ? "-" : player.stats?.goals || 0}
                                      </p>
                                    </div>

                                    <div className="text-center bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-3 group-hover:from-primary-50 group-hover:to-primary-100 transition-all duration-300 border border-gray-100 group-hover:border-primary-200 group-hover:shadow-md">
                                      <div className="w-6 h-6 mx-auto mb-1 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-300">
                                        {player.position === "Goalkeeper" ? (
                                          <svg className="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                          </svg>
                                        ) : (
                                          <svg className="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"></path>
                                          </svg>
                                        )}
                                      </div>
                                      <p className="text-xs text-gray-500 font-semibold mb-1 uppercase tracking-wide">
                                        {player.position === "Goalkeeper" ? "Clean" : "Assists"}
                                      </p>
                                      <p className="font-black text-lg text-gray-800 group-hover:text-primary-700 transition-colors">
                                        {player.position === "Goalkeeper"
                                          ? player.stats?.cleanSheets || 0
                                          : player.stats?.assists || 0}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Bio Section - Enhanced */}
                                  {player.bio && (
                                    <div className="mt-4 pt-3 border-t border-gray-200 group-hover:border-primary-200 transition-colors duration-300">
                                      <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-3 group-hover:from-primary-50 group-hover:to-primary-100 transition-all duration-300">
                                        <p className="text-xs text-gray-600 group-hover:text-gray-700 line-clamp-2 leading-relaxed">{player.bio}</p>
                                      </div>
                                    </div>
                                  )}

                                  {/* Decorative bottom accent */}
                                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-transparent via-primary-400 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                );
              }
              return null;
            })()}
          </div>
        </section>

        {/* Coaching Staff Section - Enhanced Design */}
        <section className="py-16 bg-gradient-to-br from-neutral-50 to-neutral-100">
          <div className="container">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4 text-gray-800">Coaching Staff</h2>
              <div className="w-24 h-1 bg-primary-600 mx-auto mb-4"></div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Meet the experienced professionals who guide and develop our players both on and off the field.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {staff.map((member) => (
                <div key={member._id} className="group relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 overflow-hidden border-2 border-gray-50 hover:border-primary-200">
                  {/* Decorative Background Pattern */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-primary-100/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  {/* Staff Image Section */}
                  <div className="relative h-64 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
                    {member.image ? (
                      <Image
                        src={urlForImage(member.image).url()}
                        alt={member.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500 filter group-hover:brightness-110"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 via-primary-50 to-primary-200 relative">
                        {/* Animated Background Circles */}
                        <div className="absolute inset-0 overflow-hidden">
                          <div className="absolute -top-4 -left-4 w-20 h-20 bg-primary-300/20 rounded-full group-hover:scale-150 transition-transform duration-700"></div>
                          <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-primary-400/15 rounded-full group-hover:scale-125 transition-transform duration-700 delay-100"></div>
                        </div>
                        <div className="relative z-10 p-6 text-center">
                          <div className="w-24 h-24 mx-auto mb-3 bg-primary-200 rounded-full flex items-center justify-center group-hover:bg-primary-300 transition-colors duration-300">
                            <svg className="w-12 h-12 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                          </div>
                          <p className="text-sm text-primary-600 font-medium">Photo Coming Soon</p>
                        </div>
                      </div>
                    )}

                    {/* Role Badge - Enhanced */}
                    <div className="absolute top-4 right-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white text-sm font-bold px-4 py-2 rounded-xl shadow-lg border-2 border-primary-300/50 group-hover:from-primary-500 group-hover:to-primary-600 group-hover:scale-105 transition-all duration-300">
                      <div className="flex items-center space-x-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>{member.role}</span>
                      </div>
                    </div>

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  {/* Staff Info Section */}
                  <div className="relative p-6">
                    {/* Name with decorative underline */}
                    <div className="text-center mb-4">
                      <h3 className="text-2xl font-bold text-gray-800 group-hover:text-primary-600 transition-colors duration-300 mb-2">
                        {member.name}
                      </h3>
                      <div className="w-16 h-1 bg-gradient-to-r from-primary-400 to-primary-600 rounded-full mx-auto group-hover:w-20 transition-all duration-300"></div>
                    </div>

                    {/* Bio Section */}
                    {member.bio && (
                      <div className="mb-6">
                        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 group-hover:from-primary-50 group-hover:to-primary-100 transition-all duration-300">
                          <p className="text-gray-600 group-hover:text-gray-700 leading-relaxed text-center">
                            {member.bio}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Contact Information - Enhanced */}
                    {member.contactInfo && (member.contactInfo.email || member.contactInfo.phone) && (
                      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-5 group-hover:from-primary-50 group-hover:to-primary-100 transition-all duration-300 border border-gray-100 group-hover:border-primary-200 group-hover:shadow-md">
                        <div className="text-center mb-4">
                          <h4 className="text-sm font-bold text-gray-700 group-hover:text-primary-700 uppercase tracking-wide flex items-center justify-center">
                            <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Contact Information
                          </h4>
                        </div>

                        <div className="space-y-3">
                          {member.contactInfo.email && (
                            <div className="flex items-center justify-center bg-white/50 rounded-xl p-3 group-hover:bg-white/80 transition-colors duration-300">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors duration-300">
                                <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                              </div>
                              <span className="text-sm text-gray-600 group-hover:text-gray-700 font-medium">{member.contactInfo.email}</span>
                            </div>
                          )}
                          {member.contactInfo.phone && (
                            <div className="flex items-center justify-center bg-white/50 rounded-xl p-3 group-hover:bg-white/80 transition-colors duration-300">
                              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-300">
                                <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                              </div>
                              <span className="text-sm text-gray-600 group-hover:text-gray-700 font-medium">{member.contactInfo.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Decorative bottom accent */}
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-transparent via-primary-400 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Join the Team CTA */}
        <section className="py-4 bg-primary-800 text-white">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">Join Our Team</h2>
              <p className="text-lg mb-8">
                We&apos;re always looking for new players to join our club. Whether you&apos;re experienced or just starting out, there&apos;s a place for you at Northern Nepalese United FC.
              </p>
              <a
                href="/contact"
                className="bg-white text-primary-700 hover:bg-primary-50 px-8 py-3 rounded-lg font-semibold inline-flex items-center"
              >
                Contact Us to Join
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}