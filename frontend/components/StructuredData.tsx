import Script from 'next/script'

interface StructuredDataProps {
  type?: 'organization' | 'website' | 'article' | 'event' | 'person'
  data?: any
}

export default function StructuredData({ type = 'organization', data }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://nnufc.com'

  const getStructuredData = () => {
    switch (type) {
      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'SportsOrganization',
          name: 'Northern Nepalese United Football Club',
          alternateName: 'NNUFC',
          description: 'Brisbane\'s premier Nepalese football team. Join our community, follow matches, news, events, and connect with fellow football enthusiasts in Queensland, Australia.',
          url: baseUrl,
          logo: `${baseUrl}/logo.png`,
          image: `${baseUrl}/logo.png`,
          foundingDate: '2023',
          sport: 'Football',
          address: {
            '@type': 'PostalAddress',
            addressLocality: 'Brisbane',
            addressRegion: 'Queensland',
            addressCountry: 'Australia',
            postalCode: '4053',
            streetAddress: '2 Ogg Rd, Murrumba Downs'
          },
          contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            url: `${baseUrl}/contact`
          },
          sameAs: [
            // Add social media URLs when available
            // 'https://www.facebook.com/nnufc',
            // 'https://www.instagram.com/nnufc',
            // 'https://twitter.com/nnufc'
          ],
          memberOf: {
            '@type': 'SportsLeague',
            name: 'Brisbane Football Community'
          }
        }

      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: 'Northern Nepalese United FC',
          alternateName: 'NNUFC',
          url: baseUrl,
          description: 'Official website of Northern Nepalese United Football Club - Brisbane\'s premier Nepalese football team',
          publisher: {
            '@type': 'SportsOrganization',
            name: 'Northern Nepalese United Football Club',
            logo: `${baseUrl}/logo.png`
          },
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${baseUrl}/search?q={search_term_string}`
            },
            'query-input': 'required name=search_term_string'
          }
        }

      case 'article':
        return {
          '@context': 'https://schema.org',
          '@type': 'Article',
          headline: data?.title || 'NNUFC News',
          description: data?.description || 'Latest news from Northern Nepalese United Football Club',
          image: data?.image || `${baseUrl}/logo.png`,
          author: {
            '@type': 'Organization',
            name: 'Northern Nepalese United Football Club'
          },
          publisher: {
            '@type': 'Organization',
            name: 'Northern Nepalese United Football Club',
            logo: {
              '@type': 'ImageObject',
              url: `${baseUrl}/logo.png`
            }
          },
          datePublished: data?.publishedAt || new Date().toISOString(),
          dateModified: data?.updatedAt || new Date().toISOString(),
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': data?.url || baseUrl
          }
        }

      case 'event':
        return {
          '@context': 'https://schema.org',
          '@type': 'SportsEvent',
          name: data?.title || 'NNUFC Event',
          description: data?.description || 'Northern Nepalese United Football Club event',
          startDate: data?.startDate || new Date().toISOString(),
          endDate: data?.endDate,
          location: {
            '@type': 'Place',
            name: data?.venue || 'John Oxley Reserve Field 2',
            address: {
              '@type': 'PostalAddress',
              streetAddress: '2 Ogg Rd',
              addressLocality: 'Murrumba Downs',
              addressRegion: 'Queensland',
              postalCode: '4053',
              addressCountry: 'Australia'
            }
          },
          organizer: {
            '@type': 'SportsOrganization',
            name: 'Northern Nepalese United Football Club',
            url: baseUrl
          },
          image: data?.image || `${baseUrl}/logo.png`
        }

      case 'person':
        return {
          '@context': 'https://schema.org',
          '@type': 'Person',
          name: data?.name || 'NNUFC Team Member',
          description: data?.bio || 'Member of Northern Nepalese United Football Club',
          image: data?.image,
          memberOf: {
            '@type': 'SportsOrganization',
            name: 'Northern Nepalese United Football Club'
          },
          sport: 'Football',
          nationality: data?.nationality || 'Australian'
        }

      default:
        return null
    }
  }

  const structuredData = getStructuredData()

  if (!structuredData) return null

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}
