'use client'

import { useEffect } from 'react'
import { useNNUFCAnalytics } from '../hooks/useNNUFCAnalytics'

// Example component showing how to implement analytics tracking
export default function AnalyticsExample() {
  const analytics = useNNUFCAnalytics()

  // Track page view on component mount
  useEffect(() => {
    analytics.trackNNUFCPageView('example_page', {
      component: 'AnalyticsExample',
      feature: 'demonstration'
    })
  }, [analytics])

  // Example event handlers with analytics
  const handlePlayerCardClick = () => {
    analytics.trackPlayerView('John Doe', 'Forward')
  }

  const handleNewsArticleClick = () => {
    analytics.trackNewsArticleView('NNUFC Wins Championship', 'match_results')
  }

  const handleContactFormSubmit = () => {
    analytics.trackContactFormSubmit('general_inquiry')
  }

  const handleSponsorClick = () => {
    analytics.trackSponsorClick('Mirai Global Education', 'premium')
  }

  const handleSocialMediaClick = () => {
    analytics.trackSocialMediaClick('facebook', 'header')
  }

  const handleExternalLinkClick = () => {
    analytics.trackExternalLinkClick(
      'https://example.com',
      'External Resource',
      'footer'
    )
  }

  const handleSearchSubmit = () => {
    analytics.trackSearch('player stats', 'team_page', 5)
  }

  const handleErrorExample = () => {
    analytics.trackError('user_action', 'Button click failed', 'example_page')
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 text-center">
        NNUFC Analytics Implementation Examples
      </h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* Team Interactions */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Team Interactions</h3>
          <div className="space-y-3">
            <button
              onClick={handlePlayerCardClick}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              Track Player View
            </button>
            <p className="text-sm text-gray-600">
              Tracks when users view player profiles
            </p>
          </div>
        </div>

        {/* Content Engagement */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Content Engagement</h3>
          <div className="space-y-3">
            <button
              onClick={handleNewsArticleClick}
              className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
            >
              Track News Article View
            </button>
            <p className="text-sm text-gray-600">
              Tracks news article engagement
            </p>
          </div>
        </div>

        {/* Contact & Forms */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Contact & Forms</h3>
          <div className="space-y-3">
            <button
              onClick={handleContactFormSubmit}
              className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors"
            >
              Track Form Submission
            </button>
            <p className="text-sm text-gray-600">
              Tracks contact form submissions
            </p>
          </div>
        </div>

        {/* Sponsor Interactions */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Sponsor Interactions</h3>
          <div className="space-y-3">
            <button
              onClick={handleSponsorClick}
              className="w-full bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-colors"
            >
              Track Sponsor Click
            </button>
            <p className="text-sm text-gray-600">
              Tracks sponsor link clicks
            </p>
          </div>
        </div>

        {/* Social Media */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Social Media</h3>
          <div className="space-y-3">
            <button
              onClick={handleSocialMediaClick}
              className="w-full bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 transition-colors"
            >
              Track Social Click
            </button>
            <p className="text-sm text-gray-600">
              Tracks social media link clicks
            </p>
          </div>
        </div>

        {/* External Links */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">External Links</h3>
          <div className="space-y-3">
            <button
              onClick={handleExternalLinkClick}
              className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
            >
              Track External Link
            </button>
            <p className="text-sm text-gray-600">
              Tracks external website clicks
            </p>
          </div>
        </div>

        {/* Search Functionality */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Search</h3>
          <div className="space-y-3">
            <button
              onClick={handleSearchSubmit}
              className="w-full bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600 transition-colors"
            >
              Track Search
            </button>
            <p className="text-sm text-gray-600">
              Tracks site search usage
            </p>
          </div>
        </div>

        {/* Error Tracking */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-primary-600">Error Tracking</h3>
          <div className="space-y-3">
            <button
              onClick={handleErrorExample}
              className="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors"
            >
              Track Error
            </button>
            <p className="text-sm text-gray-600">
              Tracks errors and issues
            </p>
          </div>
        </div>

        {/* Development Info */}
        <div className="bg-gray-100 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 text-gray-700">Development Mode</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Events logged to console</p>
            <p>• No data sent to analytics</p>
            <p>• Check browser dev tools</p>
            <p>• Production mode required for tracking</p>
          </div>
        </div>
      </div>

      <div className="mt-8 p-6 bg-primary-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-primary-800">
          Analytics Services Status
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong className="text-primary-700">Microsoft Clarity:</strong>
            <span className="ml-2 text-green-600">✅ Active</span>
            <p className="text-gray-600 mt-1">
              ID: {process.env.NEXT_PUBLIC_CLARITY_ID || 'Not configured'}
            </p>
          </div>
          <div>
            <strong className="text-primary-700">Google Analytics:</strong>
            <span className="ml-2 text-yellow-600">🔄 Ready for setup</span>
            <p className="text-gray-600 mt-1">
              ID: {process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'Not configured'}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
