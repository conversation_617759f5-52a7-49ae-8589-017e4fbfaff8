'use client'

import <PERSON>ript from 'next/script'
import { useEffect } from 'react'

// Analytics configuration
const CLARITY_ID = process.env.NEXT_PUBLIC_CLARITY_ID
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

// Microsoft Clarity component
function MicrosoftClarity() {
  if (!CLARITY_ID) {
    console.warn('Microsoft Clarity ID not found in environment variables')
    return null
  }

  return (
    <Script
      id="microsoft-clarity"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{
        __html: `
          (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "${CLARITY_ID}");
        `
      }}
    />
  )
}

// Google Analytics component (ready for future use)
function GoogleAnalytics() {
  if (!GA_MEASUREMENT_ID) {
    // Don't show warning in development - only when GA is actually needed
    return null
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
        strategy="afterInteractive"
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_title: document.title,
              page_location: window.location.href,
              send_page_view: true
            });
          `
        }}
      />
    </>
  )
}

// Custom event tracking functions
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  // Microsoft Clarity event tracking
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('event', eventName, parameters)
  }

  // Google Analytics event tracking (when available)
  if (typeof window !== 'undefined' && window.gtag && GA_MEASUREMENT_ID) {
    window.gtag('event', eventName, parameters)
  }

  // Console log for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Analytics Event:', eventName, parameters)
  }
}

// Page view tracking for SPA navigation
export const trackPageView = (url: string, title?: string) => {
  // Microsoft Clarity page view
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('set', 'page', url)
  }

  // Google Analytics page view (when available)
  if (typeof window !== 'undefined' && window.gtag && GA_MEASUREMENT_ID) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_title: title || document.title,
      page_location: url,
      send_page_view: true
    })
  }

  // Console log for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Analytics Page View:', url, title)
  }
}

// User identification (for authenticated users)
export const identifyUser = (userId: string, properties?: Record<string, any>) => {
  // Microsoft Clarity user identification
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('identify', userId, properties)
  }

  // Google Analytics user identification (when available)
  if (typeof window !== 'undefined' && window.gtag && GA_MEASUREMENT_ID) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      user_id: userId,
      custom_map: properties
    })
  }

  // Console log for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Analytics User Identified:', userId, properties)
  }
}

// Custom hook for analytics
export function useAnalytics() {
  useEffect(() => {
    // Track initial page view
    trackPageView(window.location.href, document.title)
  }, [])

  return {
    trackEvent,
    trackPageView,
    identifyUser
  }
}

// Main Analytics component
export default function Analytics() {
  // Only load analytics in production or when explicitly enabled
  const shouldLoadAnalytics = process.env.NODE_ENV === 'production' || 
                              process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'

  if (!shouldLoadAnalytics) {
    console.log('Analytics disabled in development mode')
    return null
  }

  return (
    <>
      <MicrosoftClarity />
      <GoogleAnalytics />
    </>
  )
}

// Type declarations for global analytics objects
declare global {
  interface Window {
    clarity: (action: string, ...args: any[]) => void
    gtag: (command: string, ...args: any[]) => void
    dataLayer: any[]
  }
}
