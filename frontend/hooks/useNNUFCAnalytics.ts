'use client'

import { trackEvent, trackPageView } from '../components/Analytics'

// Custom analytics hook specifically for NNUFC website events
export function useNNUFCAnalytics() {
  
  // Navigation tracking
  const trackNavigation = (page: string, source?: string) => {
    trackEvent('navigation', {
      page,
      source: source || 'menu',
      timestamp: new Date().toISOString()
    })
  }

  // Team-related events
  const trackPlayerView = (playerName: string, position: string) => {
    trackEvent('player_view', {
      player_name: playerName,
      position,
      section: 'team_page'
    })
  }

  const trackStaffView = (staffName: string, role: string) => {
    trackEvent('staff_view', {
      staff_name: staffName,
      role,
      section: 'team_page'
    })
  }

  // News and content engagement
  const trackNewsArticleView = (articleTitle: string, category?: string) => {
    trackEvent('news_article_view', {
      article_title: articleTitle,
      category: category || 'general',
      section: 'news'
    })
  }

  const trackNewsShare = (articleTitle: string, platform: string) => {
    trackEvent('news_share', {
      article_title: articleTitle,
      platform,
      section: 'news'
    })
  }

  // Events and fixtures
  const trackEventView = (eventTitle: string, eventType: string) => {
    trackEvent('event_view', {
      event_title: eventTitle,
      event_type: eventType,
      section: 'events'
    })
  }

  const trackEventInterest = (eventTitle: string, action: 'interested' | 'attending') => {
    trackEvent('event_interest', {
      event_title: eventTitle,
      action,
      section: 'events'
    })
  }

  // Contact and engagement
  const trackContactFormSubmit = (formType: string) => {
    trackEvent('contact_form_submit', {
      form_type: formType,
      section: 'contact'
    })
  }

  const trackContactInfoClick = (contactType: 'email' | 'phone' | 'address') => {
    trackEvent('contact_info_click', {
      contact_type: contactType,
      section: 'contact'
    })
  }

  // Sponsor interactions
  const trackSponsorClick = (sponsorName: string, sponsorTier?: string) => {
    trackEvent('sponsor_click', {
      sponsor_name: sponsorName,
      sponsor_tier: sponsorTier || 'general',
      section: 'sponsors'
    })
  }

  // Gallery interactions
  const trackGalleryImageView = (imageTitle?: string, category?: string) => {
    trackEvent('gallery_image_view', {
      image_title: imageTitle || 'untitled',
      category: category || 'general',
      section: 'gallery'
    })
  }

  // Junior Academy specific tracking
  const trackJuniorAcademyInterest = (ageGroup: string) => {
    trackEvent('junior_academy_interest', {
      age_group: ageGroup,
      section: 'junior_academy'
    })
  }

  const trackJuniorAcademyEnquiry = (ageGroup: string, contactMethod: string) => {
    trackEvent('junior_academy_enquiry', {
      age_group: ageGroup,
      contact_method: contactMethod,
      section: 'junior_academy'
    })
  }

  // Social media and external links
  const trackSocialMediaClick = (platform: string, location: string) => {
    trackEvent('social_media_click', {
      platform,
      location, // header, footer, contact page, etc.
    })
  }

  const trackExternalLinkClick = (url: string, linkText: string, section: string) => {
    trackEvent('external_link_click', {
      url,
      link_text: linkText,
      section
    })
  }

  // Search and filtering
  const trackSearch = (searchTerm: string, section: string, resultsCount?: number) => {
    trackEvent('search', {
      search_term: searchTerm,
      section,
      results_count: resultsCount
    })
  }

  const trackFilter = (filterType: string, filterValue: string, section: string) => {
    trackEvent('filter_applied', {
      filter_type: filterType,
      filter_value: filterValue,
      section
    })
  }

  // User engagement metrics
  const trackTimeOnPage = (page: string, timeSpent: number) => {
    trackEvent('time_on_page', {
      page,
      time_spent_seconds: timeSpent,
      engagement_level: timeSpent > 30 ? 'high' : timeSpent > 10 ? 'medium' : 'low'
    })
  }

  const trackScrollDepth = (page: string, scrollPercentage: number) => {
    trackEvent('scroll_depth', {
      page,
      scroll_percentage: scrollPercentage,
      engagement_milestone: scrollPercentage > 75 ? 'high' : scrollPercentage > 50 ? 'medium' : 'low'
    })
  }

  // Error tracking
  const trackError = (errorType: string, errorMessage: string, page: string) => {
    trackEvent('error_occurred', {
      error_type: errorType,
      error_message: errorMessage,
      page,
      timestamp: new Date().toISOString()
    })
  }

  // Performance tracking
  const trackPerformance = (page: string, loadTime: number) => {
    trackEvent('page_performance', {
      page,
      load_time_ms: loadTime,
      performance_rating: loadTime < 1000 ? 'excellent' : loadTime < 3000 ? 'good' : 'needs_improvement'
    })
  }

  // Custom page view tracking with NNUFC context
  const trackNNUFCPageView = (page: string, additionalData?: Record<string, any>) => {
    trackPageView(window.location.href, document.title)
    trackEvent('nnufc_page_view', {
      page,
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      timestamp: new Date().toISOString(),
      ...additionalData
    })
  }

  return {
    // Navigation
    trackNavigation,
    trackNNUFCPageView,
    
    // Team
    trackPlayerView,
    trackStaffView,
    
    // Content
    trackNewsArticleView,
    trackNewsShare,
    trackEventView,
    trackEventInterest,
    
    // Contact & Engagement
    trackContactFormSubmit,
    trackContactInfoClick,
    trackSponsorClick,
    trackGalleryImageView,
    
    // Junior Academy
    trackJuniorAcademyInterest,
    trackJuniorAcademyEnquiry,
    
    // External
    trackSocialMediaClick,
    trackExternalLinkClick,
    
    // User Behavior
    trackSearch,
    trackFilter,
    trackTimeOnPage,
    trackScrollDepth,
    
    // Technical
    trackError,
    trackPerformance
  }
}
