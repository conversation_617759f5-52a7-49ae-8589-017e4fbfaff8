// deskStructure.ts
import {StructureBuilder} from 'sanity/structure'
import {
  UsersIcon,
  DocumentIcon,
  CalendarIcon,
  ImageIcon,
  TrendUpwardIcon,
  CogIcon,
  HomeIcon,
  PlayIcon
} from '@sanity/icons'

export const customDeskStructure = (S: StructureBuilder) =>
  S.list()
    .title('Northern Nepalese United FC')
    .items([
      // Dashboard - will be handled by plugin
      // S.listItem()
      //   .title('Dashboard')
      //   .icon(HomeIcon)
      //   .child(
      //     S.component()
      //       .component(DashboardTool)
      //   ),

      S.divider(),

      // Team Management
      S.listItem()
        .title('Team Management')
        .icon(UsersIcon)
        .child(
          S.list()
            .title('Team Management')
            .items([
              S.listItem()
                .title('Players')
                .icon(UsersIcon)
                .child(
                  S.documentTypeList('player')
                    .title('Players')
                    .filter('_type == "player"')
                    .child((documentId) =>
                      S.document()
                        .documentId(documentId)
                        .schemaType('player')
                    )
                ),
              S.listItem()
                .title('Active Players')
                .icon(PlayIcon)
                .child(
                  S.documentTypeList('player')
                    .title('Active Players')
                    .filter('_type == "player" && isActive == true')
                ),
              S.listItem()
                .title('Players by Position')
                .icon(UsersIcon)
                .child(
                  S.list()
                    .title('Players by Position')
                    .items([
                      S.listItem()
                        .title('Goalkeepers')
                        .child(
                          S.documentTypeList('player')
                            .title('Goalkeepers')
                            .filter('_type == "player" && position == "Goalkeeper"')
                        ),
                      S.listItem()
                        .title('Defenders')
                        .child(
                          S.documentTypeList('player')
                            .title('Defenders')
                            .filter('_type == "player" && position == "Defender"')
                        ),
                      S.listItem()
                        .title('Midfielders')
                        .child(
                          S.documentTypeList('player')
                            .title('Midfielders')
                            .filter('_type == "player" && position == "Midfielder"')
                        ),
                      S.listItem()
                        .title('Forwards')
                        .child(
                          S.documentTypeList('player')
                            .title('Forwards')
                            .filter('_type == "player" && position == "Forward"')
                        ),
                    ])
                ),
              S.divider(),
              S.listItem()
                .title('Staff')
                .icon(CogIcon)
                .child(
                  S.documentTypeList('staff')
                    .title('Staff')
                    .filter('_type == "staff"')
                ),
            ])
        ),

      S.divider(),

      // Content Management
      S.listItem()
        .title('Content')
        .icon(DocumentIcon)
        .child(
          S.list()
            .title('Content Management')
            .items([
              S.listItem()
                .title('News Articles')
                .icon(DocumentIcon)
                .child(
                  S.documentTypeList('newsArticle')
                    .title('News Articles')
                    .filter('_type == "newsArticle"')
                ),
              S.listItem()
                .title('Published Articles')
                .child(
                  S.documentTypeList('newsArticle')
                    .title('Published Articles')
                    .filter('_type == "newsArticle" && defined(publishedAt)')
                ),
              S.listItem()
                .title('Draft Articles')
                .child(
                  S.documentTypeList('newsArticle')
                    .title('Draft Articles')
                    .filter('_type == "newsArticle" && !defined(publishedAt)')
                ),
            ])
        ),

      // Events & Fixtures
      S.listItem()
        .title('Events & Fixtures')
        .icon(CalendarIcon)
        .child(
          S.list()
            .title('Events & Fixtures')
            .items([
              S.listItem()
                .title('All Events')
                .child(
                  S.documentTypeList('event')
                    .title('All Events')
                    .filter('_type == "event"')
                ),
              S.listItem()
                .title('Upcoming Events')
                .child(
                  S.documentTypeList('event')
                    .title('Upcoming Events')
                    .filter('_type == "event" && date > now()')
                ),
              S.listItem()
                .title('Past Events')
                .child(
                  S.documentTypeList('event')
                    .title('Past Events')
                    .filter('_type == "event" && date < now()')
                ),
              S.listItem()
                .title('Matches')
                .child(
                  S.documentTypeList('event')
                    .title('Matches')
                    .filter('_type == "event" && eventType == "match"')
                ),
              S.listItem()
                .title('Training Sessions')
                .child(
                  S.documentTypeList('event')
                    .title('Training Sessions')
                    .filter('_type == "event" && eventType == "training"')
                ),
            ])
        ),

      S.divider(),

      // Media & Gallery
      S.listItem()
        .title('Media & Gallery')
        .icon(ImageIcon)
        .child(
          S.documentTypeList('galleryImage')
            .title('Gallery Images')
            .filter('_type == "galleryImage"')
        ),

      // Sponsors
      S.listItem()
        .title('Sponsors')
        .icon(TrendUpwardIcon)
        .child(
          S.documentTypeList('sponsor')
            .title('Sponsors')
            .filter('_type == "sponsor"')
        ),

      S.divider(),

      // Tools - handled by CSV Import plugin
      // S.listItem()
      //   .title('Import Tools')
      //   .icon(CogIcon)
      //   .child(
      //     S.component()
      //       .component(CsvImportTool)
      //   ),
    ])